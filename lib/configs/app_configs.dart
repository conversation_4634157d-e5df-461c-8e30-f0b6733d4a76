import 'env.dart';

class AppConfigs {
  // App Information
  static const String appName = 'Your App Name';
  static const String appVersion = '1.0.0';
  static const String appBuild = '1';

  // Environment-dependent configurations
  static String get baseUrl {
    switch (Env.environment) {
      case Environment.development:
        return 'https://dev-api.yourapp.com';
      case Environment.staging:
        return 'https://staging-api.yourapp.com';
      case Environment.production:
        return 'https://api.yourapp.com';
    }
  }

  static bool get enableLogging {
    switch (Env.environment) {
      case Environment.development:
        return true;
      case Environment.staging:
        return true;
      case Environment.production:
        return false;
    }
  }

  static bool get enableCrashlytics {
    switch (Env.environment) {
      case Environment.development:
        return false;
      case Environment.staging:
        return true;
      case Environment.production:
        return true;
    }
  }

  // Static configurations
  static const String apiVersion = 'v1';
  static const String accessTokenKey = 'access_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String userDataKey = 'user_data';

  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const int defaultPageSize = 20;

  static const String privacyPolicyUrl = 'https://yourapp.com/privacy';
  static const String termsOfServiceUrl = 'https://yourapp.com/terms';
  static const String supportEmail = '<EMAIL>';

  static const bool enableBiometric = true;
  static const bool enablePushNotifications = true;
  static const bool isDevelopment = true;
}