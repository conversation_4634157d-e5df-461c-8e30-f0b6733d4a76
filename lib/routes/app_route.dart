// lib/routes/app_route.dart - Auto Route v10.x setup
import 'package:auto_route/auto_route.dart';

import '../features/auth/presentation/screens/login_screen.dart';
import '../features/auth/presentation/screens/register_screen.dart';
import '../features/splash/presentation/screens/splash_screen.dart';

// Import your screens


part 'app_route.gr.dart';

@AutoRouterConfig()
class AppRouter extends _$AppRouter {
  @override
  List<AutoRoute> get routes => [
    AutoRoute(
      page: SplashRoute.page,
      path: '/',
      initial: true,
    ),
    AutoRoute(
      page: LoginRoute.page,
      path: '/login',
    ),
    AutoRoute(
      page: RegisterRoute.page,
      path: '/register',
    ),
  ];
}

// @RoutePage()
// class SplashWrapperRoute extends StatelessWidget {
//   const SplashWrapperRoute({super.key});
//
//   @override
//   Widget build(BuildContext context) {
//     return const SplashScreen();
//   }
// }
//
// @RoutePage()
// class LoginWrapperRoute extends StatelessWidget {
//   const LoginWrapperRoute({super.key});
//
//   @override
//   Widget build(BuildContext context) {
//     return const LoginScreen();
//   }
// }
//
// @RoutePage()
// class RegisterWrapperRoute extends StatelessWidget {
//   const RegisterWrapperRoute({super.key});
//
//   @override
//   Widget build(BuildContext context) {
//     return const RegisterScreen();
//   }
// }