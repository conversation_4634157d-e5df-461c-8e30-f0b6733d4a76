import 'package:dartz/dartz.dart';

import '../../../../shared/exceptions/http_exception.dart';
import '../models/balance_model.dart';
import '../models/banner_model.dart';
import '../models/favorite_model.dart';

abstract class HomeRepository {
  Future<Either<AppException, BalanceModel>> getBalance();
  Future<Either<AppException, void>> updateBalanceVisibility(bool isHidden);
  Future<Either<AppException, List<FavoriteModel>>> getFavorites();
  Future<Either<AppException, void>> reorderFavorites(List<String> orderedIds);
  Future<Either<AppException, List<BannerModel>>> getBanners();
}