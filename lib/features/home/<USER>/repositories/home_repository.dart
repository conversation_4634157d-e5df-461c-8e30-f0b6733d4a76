import 'package:dartz/dartz.dart';

import '../../../../core/network/network_info.dart';
import '../../../../core/sync/sync_manager.dart';
import '../../../../shared/exceptions/http_exception.dart';
import '../../domain/entities/balance_entity.dart';
import '../../domain/entities/banner_entity.dart';
import '../../domain/entities/favorite_entity.dart';
import '../datasource/home_local_datasource.dart';
import '../datasource/home_remote_datasource.dart';

abstract class HomeRepository {
  Future<Either<AppException, BalanceEntity>> getBalance();
  Future<Either<AppException, void>> updateBalanceVisibility(bool isHidden);
  Future<Either<AppException, List<FavoriteEntity>>> getFavorites();
  // Future<Either<AppException, void>> reorderFavorites(List<String> orderedIds);
  Future<Either<AppException, List<BannerEntity>>> getBanners();
}

class HomeRepositoryImpl implements HomeRepository {
  final HomeRemoteDatasource remoteDatasource;
  final HomeLocalDatasource localDatasource;
  final NetworkInfo networkInfo;
  final SyncManagerImpl syncManager;

  HomeRepositoryImpl(
      this.remoteDatasource,
      this.localDatasource,
      this.networkInfo,
      this.syncManager,
      );

  @override
  Future<Either<AppException, BalanceEntity>> getBalance() async {
    try {
      // Return cached data first
      final cachedBalance = await localDatasource.getCachedBalance();

      // Check if we should refresh
      final lastSync = await localDatasource.getLastSyncTime('balance');
      final shouldRefresh = lastSync == null ||
          DateTime.now().difference(lastSync).inMinutes > 15;

      if (cachedBalance != null && !shouldRefresh) {
        return Right(cachedBalance);
      }

      // Try to fetch from remote if connected
      if (await networkInfo.isConnected) {
        final result = await remoteDatasource.getBalance();

        return result.fold(
              (error) {
            // Return cached data on error
            return cachedBalance != null
                ? Right(cachedBalance)
                : Left(error);
          },
              (balanceModel) async {
            final entity = balanceModel.toEntity();
            await localDatasource.cacheBalance(entity);
            await localDatasource.setLastSyncTime('balance', DateTime.now());
            return Right(entity);
          },
        );
      } else {
        // Offline - return cached data
        return cachedBalance != null
            ? Right(cachedBalance)
            : const Left(AppException.network('No internet connection'));
      }
    } catch (e) {
      final cachedBalance = await localDatasource.getCachedBalance();
      return cachedBalance != null
          ? Right(cachedBalance)
          : Left(AppException.unknown(e.toString()));
    }
  }

  @override
  Future<Either<AppException, void>> updateBalanceVisibility(bool isHidden) async {
    try {
      // Update local cache immediately
      final currentBalance = await localDatasource.getCachedBalance();
      if (currentBalance != null) {
        final updatedBalance = currentBalance.copyWith(isHidden: isHidden);
        await localDatasource.cacheBalance(updatedBalance);
      }

      // Queue for sync
      await syncManager.queueForSync('update_balance_visibility', {
        'isHidden': isHidden,
      });

      // Try to sync immediately if online
      if (await networkInfo.isConnected) {
        final result = await remoteDatasource.updateBalanceVisibility(isHidden);
        result.fold(
              (error) {}, // Keep in sync queue
              (_) async {
            await syncManager.removeSyncOperation('update_balance_visibility', 'balance');
          },
        );
      }

      return const Right(null);
    } catch (e) {
      return  Left(AppException.unknown(e.toString()));

  }
  }

  @override
  Future<Either<AppException, List<FavoriteEntity>>> getFavorites() async {
    try {
      // Return cached data first
      final cachedFavorites = await localDatasource.getCachedFavorites();

      // Check if we should refresh
      final lastSync = await localDatasource.getLastSyncTime('favorites');
      final shouldRefresh = lastSync == null ||
          DateTime.now().difference(lastSync).inHours > 6;

      if (cachedFavorites.isNotEmpty && !shouldRefresh) {
        return Right(cachedFavorites);
      }

      // Try to fetch from remote if connected
      if (await networkInfo.isConnected) {
        final result = await remoteDatasource.getFavorites();

        return result.fold(
              (error) {
            return cachedFavorites.isNotEmpty
                ? Right(cachedFavorites)
                : Left(error);
          },
              (favoriteModels) async {
            final entities = favoriteModels.map((m) => m.toEntity()).toList();
            await localDatasource.cacheFavorites(entities);
            await localDatasource.setLastSyncTime('favorites', DateTime.now());
            return Right(entities);
          },
        );
      } else {
        return cachedFavorites.isNotEmpty
            ? Right(cachedFavorites)
            : const Left(AppException.network('No internet connection'));
      }
    } catch (e) {
      final cachedFavorites = await localDatasource.getCachedFavorites();
      return cachedFavorites.isNotEmpty
          ? Right(cachedFavorites)
          : Left(AppException.unknown(e.toString()));
    }
  }

  // @override
  // Future<Either<AppException, void>> reorderFavorites(List<String> orderedIds) async {
  //   try {
  //     // Update local cache immediately (optimistic update)
  //     final currentFavorites = await localDatasource.getCachedFavorites();
  //     final reorderedFavorites = <FavoriteEntity>[];
  //
  //     for (int i = 0; i < orderedIds.length; i++) {
  //       final favorite = currentFavorites.firstWhere((f) => f.id == orderedIds[i]);
  //       reorderedFavorites.add(favorite.copyWith(sortOrder: i));
  //     }
  //
  //     await localDatasource.cacheFavorites(reorderedFavorites);
  //
  //     // Queue for sync
  //     await syncManager.queueForSync('reorder_favorites', {
  //       'order': orderedIds,
  //     });
  //
  //     // Try to sync immediately if online
  //     if (await networkInfo.isConnected) {
  //       final result = await remoteDatasource.reorderFavorites(orderedIds);
  //       result.fold(
  //             (error) {}, // Keep in sync queue
  //             (_) async {
  //           await syncManager.removeSyncOperation('reorder_favorites', 'favorites');
  //         },
  //       );
  //     }
  //
  //     return const Right(null);
  //   } catch (e) {
  //     return Left(AppException.unknownError(e.toString()));
  //   }
  // }

  @override
  Future<Either<AppException, List<BannerEntity>>> getBanners() async {
    try {
      final cachedBanners = await localDatasource.getCachedBanners();

      final lastSync = await localDatasource.getLastSyncTime('banners');
      final shouldRefresh = lastSync == null ||
          DateTime.now().difference(lastSync).inHours > 2;

      if (cachedBanners.isNotEmpty && !shouldRefresh) {
        return Right(cachedBanners);
      }

      if (await networkInfo.isConnected) {
        final result = await remoteDatasource.getBanners();

        return result.fold(
              (error) {
            return cachedBanners.isNotEmpty
                ? Right(cachedBanners)
                : Left(error);
          },
              (bannerModels) async {
            final entities = bannerModels.map((m) => m.toEntity()).toList();
            await localDatasource.cacheBanners(entities);
            await localDatasource.setLastSyncTime('banners', DateTime.now());
            return Right(entities.where((b) => b.isValid).toList());
          },
        );
      } else {
        return cachedBanners.isNotEmpty
            ? Right(cachedBanners)
            : const Left(AppException.network('No internet connection'));
      }
    } catch (e) {
      final cachedBanners = await localDatasource.getCachedBanners();
      return cachedBanners.isNotEmpty
          ? Right(cachedBanners)
          : const Left(AppException.network('No internet connection'));
    }
  }
}