import '../../../../core/database/app_database.dart';
import '../../domain/entities/balance_entity.dart';
import '../../domain/entities/banner_entity.dart';
import '../../domain/entities/favorite_entity.dart';
import '../../domain/models/balance_model.dart';
import '../../domain/models/banner_model.dart';
import '../../domain/models/favorite_model.dart';

abstract class HomeLocalDatasource {
  Future<BalanceEntity?> getCachedBalance();
  Future<void> cacheBalance(BalanceEntity balance);
  Future<List<FavoriteEntity>> getCachedFavorites();
  Future<void> cacheFavorites(List<FavoriteEntity> favorites);
  Future<List<BannerEntity>> getCachedBanners();
  Future<void> cacheBanners(List<BannerEntity> banners);
  Future<DateTime?> getLastSyncTime(String key);
  Future<void> setLastSyncTime(String key, DateTime time);
}

class HomeLocalDatasourceImpl implements HomeLocalDatasource {
  @override
  Future<BalanceEntity?> getCachedBalance() async {
    final data = AppDatabase.homeBox.get('balance');
    if (data == null) return null;

    final model = BalanceModel.fromJson(Map<String, dynamic>.from(data));
    return model.toEntity();
  }

  @override
  Future<void> cacheBalance(BalanceEntity balance) async {
    final model = BalanceModel(
      amount: balance.amount,
      currency: balance.currency,
      isHidden: balance.isHidden,
      lastUpdated: balance.lastUpdated.toIso8601String(),
    );

    await AppDatabase.homeBox.put('balance', model.toJson());
  }

  @override
  Future<List<FavoriteEntity>> getCachedFavorites() async {
    final data = AppDatabase.homeBox.get('favorites', defaultValue: <Map<String, dynamic>>[]);
    return (data as List)
        .map((json) => FavoriteModel.fromJson(Map<String, dynamic>.from(json)))
        .map((model) => model.toEntity())
        .toList();
  }

  @override
  Future<void> cacheFavorites(List<FavoriteEntity> favorites) async {
    final models = favorites.map((entity) => FavoriteModel(
      id: entity.id,
      name: entity.name,
      category: entity.category,
      iconName: entity.iconName,
      iconUrl: entity.iconUrl,
      colorHex: entity.colorHex,
      sortOrder: entity.sortOrder,
      isActive: entity.isActive,
      lastSync: entity.lastSync?.toIso8601String(),
    )).toList();

    await AppDatabase.homeBox.put('favorites', models.map((m) => m.toJson()).toList());
  }

  @override
  Future<List<BannerEntity>> getCachedBanners() async {
    final data = AppDatabase.homeBox.get('banners', defaultValue: <Map<String, dynamic>>[]);
    return (data as List)
        .map((json) => BannerModel.fromJson(Map<String, dynamic>.from(json)))
        .map((model) => model.toEntity())
        .where((banner) => banner.isValid)
        .toList();
  }

  @override
  Future<void> cacheBanners(List<BannerEntity> banners) async {
    final models = banners.map((entity) => BannerModel(
      id: entity.id,
      title: entity.title,
      description: entity.description,
      imageUrl: entity.imageUrl,
      actionUrl: entity.actionUrl,
      sortOrder: entity.sortOrder,
      validUntil: entity.validUntil?.toIso8601String(),
      lastSync: entity.lastSync?.toIso8601String(),
    )).toList();

    await AppDatabase.homeBox.put('banners', models.map((m) => m.toJson()).toList());
  }

  @override
  Future<DateTime?> getLastSyncTime(String key) async {
    final timestamp = AppDatabase.homeBox.get('last_sync_$key');
    return timestamp != null ? DateTime.parse(timestamp) : null;
  }

  @override
  Future<void> setLastSyncTime(String key, DateTime time) async {
    await AppDatabase.homeBox.put('last_sync_$key', time.toIso8601String());
  }
}
