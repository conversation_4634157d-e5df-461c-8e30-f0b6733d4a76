import 'package:dartz/dartz.dart';

import '../../../../shared/data/remote/network_service.dart';
import '../../../../shared/domain/models/response.dart';
import '../../../../shared/exceptions/http_exception.dart';
import '../../../../shared/mixins/exception_handler_mixin.dart';
import '../../domain/models/balance_model.dart';
import '../../domain/models/banner_model.dart';
import '../../domain/models/favorite_model.dart';

abstract class HomeRemoteDatasource {
  Future<Either<AppException, BalanceModel>> getBalance();

  Future<Either<AppException, void>> updateBalanceVisibility(bool isHidden);

  Future<Either<AppException, List<FavoriteModel>>> getFavorites();

  // Future<Either<AppException, void>> reorderFavorites(List<String> orderedIds);

  Future<Either<AppException, List<BannerModel>>> getBanners();
}

class HomeRemoteDatasourceImpl extends HomeRemoteDatasource
    with ExceptionHandlerMixin {
  final NetworkService networkService;

  HomeRemoteDatasourceImpl(this.networkService);

  @override
  Future<Either<AppException, BalanceModel>> getBalance() async {
    final result = await networkService.get('/user/balance');

    return result.fold(
          (failure) => Left(failure),
          (response) {
        try {
          // Cast dynamic to Map<String, dynamic>
          final apiResponse =
          ApiResponse.fromJson(response as Map<String, dynamic>);
          // EXACTLY WHAT YOU WANT - Direct JSON parsing
          return apiResponse
              .parseData<BalanceModel>((data) => BalanceModel.fromJson(data));
        } catch (e) {
          return Left(AppException.unknown(
              'Failed to parse refresh token response: ${e.toString()}'));
        }
      },
    );
  }

  @override
  Future<Either<AppException, void>> updateBalanceVisibility(
      bool isHidden) async {
    final result = await networkService.put(
      '/user/balance/visibility',
      data: {'isHidden': isHidden},
    );

    return result.fold(
          (failure) => Left(failure),
          (_) {
        try {
          return const Right(null); // Success case, return void (null)
        } catch (e) {
          return Left(AppException.unknown(
            'Failed to parse response: ${e.toString()}',
          ));
        }
      },
    );
  }


  @override
  Future<Either<AppException, List<FavoriteModel>>> getFavorites() async {
    final result = await networkService.get('/user/favorites');

    return result.fold(
          (failure) => Left(failure),
          (response) {
        try {
          if (response is! Map<String, dynamic>) {
            return const Left(
                AppException.unknown('Unexpected response format'));
          }

          final apiResponse = ApiResponse.fromJson(response);

          // Make sure you're parsing a list from the 'data' field
          final data = apiResponse.data;

          if (data is! List) {
            return const Left(AppException.unknown('Expected a list in data'));
          }

          final favorites = data
              .map((item) =>
              FavoriteModel.fromJson(item as Map<String, dynamic>))
              .toList();

          return Right(favorites);
        } catch (e) {
          return Left(
            AppException.unknown(
              'Failed to parse favorites: ${e.toString()}',
            ),
          );
        }
      },
    );
  }

  // @override
  // Future<Either<AppException, void>> reorderFavorites(List<String> orderedIds) {
  //   return exceptionHandler(() async {
  //     final response = await networkService.put('/user/favorites/order', data: {
  //       'order': orderedIds,
  //     });
  //     return response.fold(
  //       (error) => throw error,
  //       (_) => null,
  //     );
  //   });

  @override
  Future<Either<AppException, List<BannerModel>>> getBanners() async {
    final result = await networkService.get('/banners');

    return result.fold(
          (failure) => Left(failure),
          (response) {
        try {
          if (response is! Map<String, dynamic>) {
            return const Left(
                AppException.unknown('Unexpected response format'));
          }

          final apiResponse = ApiResponse.fromJson(response);

          // Make sure you're parsing a list from the 'data' field
          final data = apiResponse.data;

          if (data is! List) {
            return const Left(AppException.unknown('Expected a list in data'));
          }

          final favorites = data
              .map((item) =>
              BannerModel.fromJson(item as Map<String, dynamic>))
              .toList();

          return Right(favorites);
        } catch (e) {
          return Left(
            AppException.unknown(
              'Failed to parse favorites: ${e.toString()}',
            ),
          );
        }
      },
    );
  }
}
