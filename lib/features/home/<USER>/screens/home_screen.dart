// lib/features/home/<USER>/screens/qubli_home_screen.dart
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../shared/utils/responsive_utils.dart';
import '../../../../shared/widgets/responsive_widget.dart';
import '../../../../shared/theme/app_colors.dart';
import '../../domain/entities/balance_entity.dart';
import '../../domain/entities/banner_entity.dart';
import '../../domain/entities/favorite_entity.dart';
import '../providers/home_provider.dart';
import '../providers/state/home_notifier.dart';
import '../providers/state/home_state.dart';

class QubliHomeScreen extends ConsumerWidget {
  const QubliHomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final homeState = ref.watch(homeNotifierProvider);
    final homeNotifier = ref.read(homeNotifierProvider.notifier);

    return ResponsiveLayout(
      mobile: _buildMobileLayout(context, homeState, homeNotifier),
      tablet: _buildTabletLayout(context, homeState, homeNotifier),
    );
  }

  Widget _buildMobileLayout(BuildContext context, HomeState homeState, HomeNotifier homeNotifier) {
    return ResponsiveContainer(
      useHorizontalPadding: false,
      child: Column(
        children: [
          // Independent Balance Card
          ResponsiveContainer(
            useHorizontalPadding: true,
            child: IndependentBalanceCard(
              balance: homeState.balance,
              isLoading: homeState.isBalanceLoading,
              error: homeState.balanceError,
              isOffline: homeState.isOffline,
              onRefresh: homeNotifier.refreshBalance,
              onToggleVisibility: homeNotifier.toggleBalanceVisibility,
            ),
          ),

          const ResponsiveSpacing(size: SpacingSize.lg),

          // Independent Favorites Section
          ResponsiveContainer(
            useHorizontalPadding: true,
            child: IndependentFavoritesSection(
              favorites: homeState.favorites,
              isLoading: homeState.isFavoritesLoading,
              error: homeState.favoritesError,
              isOffline: homeState.isOffline,
              pendingSyncCount: homeState.pendingSyncCount,
              onRefresh: homeNotifier.refreshFavorites,
              onReorder: homeNotifier.reorderFavorites,
            ),
          ),

          const ResponsiveSpacing(size: SpacingSize.lg),

          // Independent Banners Section
          ResponsiveContainer(
            useHorizontalPadding: true,
            child: IndependentBannersSection(
              banners: homeState.banners,
              isLoading: homeState.isBannersLoading,
              error: homeState.bannersError,
              isOffline: homeState.isOffline,
              onRefresh: homeNotifier.refreshBanners,
            ),
          ),

          const ResponsiveSpacing(size: SpacingSize.xl),
        ],
      ),
    );
  }

  Widget _buildTabletLayout(BuildContext context, HomeState homeState, HomeNotifier homeNotifier) {
    return ResponsiveContainer(
      useHorizontalPadding: true,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Left column - Balance and Quick Actions
          Expanded(
            flex: 1,
            child: Column(
              children: [
                IndependentBalanceCard(
                  balance: homeState.balance,
                  isLoading: homeState.isBalanceLoading,
                  error: homeState.balanceError,
                  isOffline: homeState.isOffline,
                  onRefresh: homeNotifier.refreshBalance,
                  onToggleVisibility: homeNotifier.toggleBalanceVisibility,
                ),
                const ResponsiveSpacing(size: SpacingSize.lg),
                // Additional quick actions for tablet layout
                _buildQuickActions(context),
              ],
            ),
          ),

          const ResponsiveSpacing(size: SpacingSize.lg, direction: Axis.horizontal),

          // Right column - Favorites and Banners
          Expanded(
            flex: 1,
            child: Column(
              children: [
                IndependentFavoritesSection(
                  favorites: homeState.favorites,
                  isLoading: homeState.isFavoritesLoading,
                  error: homeState.favoritesError,
                  isOffline: homeState.isOffline,
                  pendingSyncCount: homeState.pendingSyncCount,
                  onRefresh: homeNotifier.refreshFavorites,
                  onReorder: homeNotifier.reorderFavorites,
                ),
                const ResponsiveSpacing(size: SpacingSize.lg),
                IndependentBannersSection(
                  banners: homeState.banners,
                  isLoading: homeState.isBannersLoading,
                  error: homeState.bannersError,
                  isOffline: homeState.isOffline,
                  onRefresh: homeNotifier.refreshBanners,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, dimensions) {
        return Container(
          padding: EdgeInsets.all(context.spacing.md),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              const ResponsiveText(
                'Quick Actions',
                type: TextType.h4,
              ),
              const ResponsiveSpacing(size: SpacingSize.md),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildQuickActionItem(
                    context,
                    icon: Icons.send,
                    label: 'Transfer',
                    onTap: () {},
                  ),
                  _buildQuickActionItem(
                    context,
                    icon: Icons.payment,
                    label: 'Pay Bills',
                    onTap: () {},
                  ),
                  _buildQuickActionItem(
                    context,
                    icon: Icons.add_circle,
                    label: 'Top Up',
                    onTap: () {},
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildQuickActionItem(
      BuildContext context, {
        required IconData icon,
        required String label,
        required VoidCallback onTap,
      }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: AppColors.primary,
              size: 24,
            ),
          ),
          const ResponsiveSpacing(size: SpacingSize.xs),
          ResponsiveText(
            label,
            type: TextType.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

// ========== INDEPENDENT BALANCE CARD ==========
class IndependentBalanceCard extends StatelessWidget {
  final BalanceEntity? balance;
  final bool isLoading;
  final String? error;
  final bool isOffline;
  final VoidCallback onRefresh;
  final VoidCallback onToggleVisibility;

  const IndependentBalanceCard({
  super.key,
  required this.balance,
  required this.isLoading,
  this.error,
  required this.isOffline,
  required this.onRefresh,
  required this.onToggleVisibility,
  });

  @override
  Widget build(BuildContext context) {
    // Show skeleton while loading (no data)
    if (isLoading && balance == null) {
      return _buildLoadingSkeleton(context);
    }

    // Show error state with retry (no data)
    if (error != null && balance == null) {
      return ResponsiveErrorCard(
        title: 'Unable to load balance',
        message: error!,
        onRetry: onRefresh,
      );
    }

    // Show content with optional refresh indicator
    return ResponsiveBuilder(
      builder: (context, dimensions) {
        return Stack(
          children: [
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(context.spacing.lg),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [AppColors.primary, AppColors.primary.withOpacity(0.8)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withOpacity(0.3),
                    blurRadius: 15,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with refresh button
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const ResponsiveText(
                        'Saldo Anda',
                        type: TextType.bodyMedium,
                        color: Colors.white70,
                      ),
                      Row(
                        children: [
                          if (isOffline) _buildOfflineIndicator(),
                          const SizedBox(width: 8),
                          GestureDetector(
                            onTap: isLoading ? null : onRefresh,
                            child: Icon(
                              Icons.refresh,
                              color: isLoading ? Colors.white54 : Colors.white,
                              size: dimensions.isTablet ? 24 : 20,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),

                  const ResponsiveSpacing(size: SpacingSize.sm),

                  // Balance display
                  Row(
                    children: [
                      Expanded(
                        child: ResponsiveText(
                          balance?.isHidden == true
                              ? '${balance?.currency ?? 'Rp'} ••••••••'
                              : '${balance?.currency ?? 'Rp'} ${_formatAmount(balance?.amount ?? 0)}',
                          type: dimensions.isTablet ? TextType.h2 : TextType.h3,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      GestureDetector(
                        onTap: onToggleVisibility,
                        child: Icon(
                          balance?.isHidden == true ? Icons.visibility : Icons.visibility_off,
                          color: Colors.white,
                          size: dimensions.isTablet ? 24 : 20,
                        ),
                      ),
                    ],
                  ),

                  const ResponsiveSpacing(size: SpacingSize.md),

                  // Quick actions
                  dimensions.isTablet
                      ? _buildTabletActions(context)
                      : _buildMobileActions(context),
                ],
              ),
            ),

            // Loading overlay when refreshing existing content
            if (isLoading && balance != null)
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  width: 20,
                  height: 20,
                  child: const CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  Widget _buildLoadingSkeleton(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, dimensions) {
        return Container(
          width: double.infinity,
          height: dimensions.isTablet ? 200 : 160,
          padding: EdgeInsets.all(context.spacing.lg),
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(20),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                height: 16,
                width: 100,
                decoration: BoxDecoration(
                  color: Colors.grey[400],
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              const ResponsiveSpacing(size: SpacingSize.md),
              Container(
                height: dimensions.isTablet ? 32 : 28,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.grey[400],
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              const ResponsiveSpacing(size: SpacingSize.lg),
              Row(
                children: List.generate(
                  dimensions.isTablet ? 3 : 4,
                      (index) => Expanded(
                    child: Container(
                      height: 60,
                      margin: const EdgeInsets.only(right: 8),
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMobileActions(BuildContext context) {
    return Row(
      children: [
        Expanded(child: _buildActionButton(context, Icons.send, 'Transfer')),
        const SizedBox(width: 8),
        Expanded(child: _buildActionButton(context, Icons.payment, 'Pay')),
        const SizedBox(width: 8),
        Expanded(child: _buildActionButton(context, Icons.add_circle, 'Top Up')),
        const SizedBox(width: 8),
        Expanded(child: _buildActionButton(context, Icons.more_horiz, 'More')),
      ],
    );
  }

  Widget _buildTabletActions(BuildContext context) {
    return Row(
      children: [
        Expanded(child: _buildActionButton(context, Icons.send, 'Transfer')),
        const SizedBox(width: 12),
        Expanded(child: _buildActionButton(context, Icons.payment, 'Pay Bills')),
        const SizedBox(width: 12),
        Expanded(child: _buildActionButton(context, Icons.add_circle, 'Top Up')),
      ],
    );
  }

  Widget _buildActionButton(BuildContext context, IconData icon, String label) {
    return ResponsiveBuilder(
      builder: (context, dimensions) {
        return GestureDetector(
          onTap: () {},
          child: Container(
            padding: EdgeInsets.symmetric(
              vertical: dimensions.isTablet ? 12 : 8,
              horizontal: 8,
            ),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                Icon(
                  icon,
                  color: Colors.white,
                  size: dimensions.isTablet ? 24 : 20,
                ),
                const SizedBox(height: 4),
                ResponsiveText(
                  label,
                  type: dimensions.isTablet ? TextType.bodySmall : TextType.caption,
                  color: Colors.white,
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildOfflineIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.orange,
        borderRadius: BorderRadius.circular(8),
      ),
      child: const ResponsiveText(
        'Offline',
        type: TextType.caption,
        color: Colors.white,
      ),
    );
  }

  String _formatAmount(double amount) {
    // Simple formatting - you can enhance this
    if (amount >= 1000000) {
      return '${(amount / 1000000).toStringAsFixed(1)}M';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)}K';
    }
    return amount.toStringAsFixed(0);
  }
}

// ========== INDEPENDENT FAVORITES SECTION ==========
class IndependentFavoritesSection extends StatelessWidget {
  final List<FavoriteEntity> favorites;
  final bool isLoading;
  final String? error;
  final bool isOffline;
  final int pendingSyncCount;
  final VoidCallback onRefresh;
  final Function(List<String>) onReorder;

  const IndependentFavoritesSection({
  super.key,
  required this.favorites,
  required this.isLoading,
  this.error,
  required this.isOffline,
  required this.pendingSyncCount,
  required this.onRefresh,
  required this.onReorder,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header with status indicators
        Row(
          children: [
            const ResponsiveText(
              'Favorit',
              type: TextType.h4,
            ),
            const Spacer(),
            if (isOffline) _buildIndicator('Offline', Colors.orange),
            if (pendingSyncCount > 0) ...[
              const SizedBox(width: 8),
              _buildIndicator('$pendingSyncCount sync', Colors.blue),
            ],
            const SizedBox(width: 8),
            GestureDetector(
              onTap: isLoading ? null : onRefresh,
              child: Icon(
                Icons.refresh,
                color: isLoading ? Colors.grey : AppColors.primary,
                size: 20,
              ),
            ),
          ],
        ),

        const ResponsiveSpacing(size: SpacingSize.md),

        // Content based on individual state
        if (isLoading && favorites.isEmpty)
          _buildLoadingSkeleton(context)
        else if (error != null && favorites.isEmpty)
          ResponsiveErrorCard(
            title: 'Unable to load favorites',
            message: error!,
            onRetry: onRefresh,
          )
        else
          _buildFavoritesGrid(context),
      ],
    );
  }

  Widget _buildFavoritesGrid(BuildContext context) {
    return Stack(
      children: [
        ResponsiveBuilder(
          builder: (context, dimensions) {
            final crossAxisCount = dimensions.isTablet ? 6 : 4;
            final aspectRatio = dimensions.isTablet ? 1.0 : 0.8;

            return GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: crossAxisCount,
                childAspectRatio: aspectRatio,
                crossAxisSpacing: context.spacing.sm,
                mainAxisSpacing: context.spacing.sm,
              ),
              itemCount: favorites.length,
              itemBuilder: (context, index) {
                return ResponsiveFavoriteItem(
                  service: favorites[index],
                  isOffline: isOffline,
                  onTap: () => _handleServiceTap(context, favorites[index]),
                );
              },
            );
          },
        ),

        // Loading overlay when refreshing existing content
        if (isLoading && favorites.isNotEmpty)
          Positioned(
            top: 0,
            right: 0,
            child: Container(
              width: 20,
              height: 20,
              child: const CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildLoadingSkeleton(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, dimensions) {
        final crossAxisCount = dimensions.isTablet ? 6 : 4;

        return GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            childAspectRatio: dimensions.isTablet ? 1.0 : 0.8,
            crossAxisSpacing: context.spacing.sm,
            mainAxisSpacing: context.spacing.sm,
          ),
          itemCount: 8,
          itemBuilder: (context, index) {
            return Column(
              children: [
                Container(
                  width: dimensions.isTablet ? 50 : 40,
                  height: dimensions.isTablet ? 50 : 40,
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  height: 12,
                  width: dimensions.isTablet ? 60 : 50,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Widget _buildIndicator(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(8),
      ),
      child: ResponsiveText(
        text,
        type: TextType.caption,
        color: Colors.white,
      ),
    );
  }

  void _handleServiceTap(BuildContext context, FavoriteEntity service) {
    // Handle service tap
    print('Tapped on ${service.name}');
  }
}

// ========== RESPONSIVE FAVORITE ITEM ==========
class ResponsiveFavoriteItem extends StatelessWidget {
  final FavoriteEntity service;
  final bool isOffline;
  final VoidCallback onTap;

  const ResponsiveFavoriteItem({
  super.key,
  required this.service,
  required this.isOffline,
  required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, dimensions) {
        return GestureDetector(
          onTap: onTap,
          child: Container(
            padding: EdgeInsets.all(context.spacing.sm),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 5,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Stack(
                  children: [
                    Container(
                      width: dimensions.isTablet ? 40 : 32,
                      height: dimensions.isTablet ? 40 : 32,
                      decoration: BoxDecoration(
                        color: AppColors.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: _buildIcon(dimensions),
                      ),
                    ),

                    // Offline indicator
                    if (isOffline && service.lastSync != null)
                      Positioned(
                        top: -2,
                        right: -2,
                        child: Container(
                          width: 8,
                          height: 8,
                          decoration: const BoxDecoration(
                            color: Colors.orange,
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                  ],
                ),

                const SizedBox(height: 4),

                ResponsiveText(
                  service.name,
                  type: dimensions.isTablet ? TextType.bodySmall : TextType.caption,
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildIcon(ResponsiveDimensions dimensions) {
    final iconSize = dimensions.isTablet ? 20.0 : 16.0;

    // Priority: iconUrl > iconName > default
    if (service.iconUrl != null && service.iconUrl!.isNotEmpty) {
      return _buildNetworkIcon(service.iconUrl!, iconSize);
    } else {
      return _buildFallbackIcon(service.iconName, iconSize);
    }
  }

  Widget _buildNetworkIcon(String iconUrl, double size) {
    return Image.network(
      iconUrl,
      width: size,
      height: size,
      fit: BoxFit.contain,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;

        return SizedBox(
          width: size,
          height: size,
          child: Center(
            child: SizedBox(
              width: size * 0.6,
              height: size * 0.6,
              child: CircularProgressIndicator(
                strokeWidth: 1.5,
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary.withOpacity(0.7)),
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded /
                    loadingProgress.expectedTotalBytes!
                    : null,
              ),
            ),
          ),
        );
      },
      errorBuilder: (context, error, stackTrace) {
        // Fallback to icon name if URL fails
        return _buildFallbackIcon(service.iconName, size);
      },
    );
  }

  Widget _buildFallbackIcon(String? iconName, double size) {
    return Icon(
      _getIcon(iconName),
      size: size,
      color: AppColors.primary,
    );
  }

  IconData _getIcon(String? iconName) {
    switch (iconName?.toLowerCase()) {
      case 'transfer':
        return Icons.send;
      case 'payment':
        return Icons.payment;
      case 'topup':
        return Icons.add_circle;
      case 'history':
        return Icons.history;
      case 'voucher':
        return Icons.local_offer;
      case 'invest':
        return Icons.trending_up;
      case 'insurance':
        return Icons.security;
      case 'qr':
        return Icons.qr_code_scanner;
      case 'bills':
        return Icons.receipt_long;
      case 'food':
        return Icons.restaurant;
      case 'shopping':
        return Icons.shopping_cart;
      case 'taxi':
        return Icons.local_taxi;
      case 'movie':
        return Icons.movie;
      case 'health':
        return Icons.medical_services;
      case 'education':
        return Icons.school;
      case 'games':
        return Icons.sports_esports;
      case 'more':
        return Icons.more_horiz;
      default:
        return Icons.apps;
    }
  }
}

// ========== INDEPENDENT BANNERS SECTION ==========
class IndependentBannersSection extends StatelessWidget {
  final List<BannerEntity> banners;
  final bool isLoading;
  final String? error;
  final bool isOffline;
  final VoidCallback onRefresh;

  const IndependentBannersSection({
  super.key,
  required this.banners,
  required this.isLoading,
  this.error,
  required this.isOffline,
  required this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header with refresh
        Row(
          children: [
            const ResponsiveText(
              'Promo Spesial untuk Kamu!',
              type: TextType.h4,
            ),
            const Spacer(),
            if (isOffline) _buildOfflineIndicator(),
            const SizedBox(width: 8),
            GestureDetector(
              onTap: isLoading ? null : onRefresh,
              child: Icon(
                Icons.refresh,
                color: isLoading ? Colors.grey : AppColors.primary,
                size: 20,
              ),
            ),
          ],
        ),

        const ResponsiveSpacing(size: SpacingSize.md),

        // Individual loading states
        if (isLoading && banners.isEmpty)
          _buildLoadingSkeleton(context)
        else if (error != null && banners.isEmpty)
          ResponsiveErrorCard(
            title: 'Unable to load promos',
            message: error!,
            onRetry: onRefresh,
          )
        else
          _buildBannersCarousel(context),
      ],
    );
  }

  Widget _buildBannersCarousel(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, dimensions) {
        final height = dimensions.isTablet ? 160.0 : 120.0;

        return SizedBox(
          height: height,
          child: PageView.builder(
            itemCount: banners.length,
            itemBuilder: (context, index) {
              return Container(
                margin: EdgeInsets.only(right: context.spacing.md),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16),
                  child: Stack(
                    children: [
                      // Banner image or placeholder
                      Container(
                        width: double.infinity,
                        height: height,
                        color: AppColors.primary.withOpacity(0.1),
                        child: banners[index].imageUrl != null
                            ? Image.network(
                          banners[index].imageUrl!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return _buildBannerPlaceholder(context, banners[index]);
                          },
                        )
                            : _buildBannerPlaceholder(context, banners[index]),
                      ),

                      // Banner content overlay
                      Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: Container(
                          padding: EdgeInsets.all(context.spacing.md),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Colors.transparent,
                                Colors.black.withOpacity(0.7),
                              ],
                            ),
                          ),
                          child: ResponsiveText(
                            banners[index].title,
                            type: dimensions.isTablet ? TextType.bodyMedium : TextType.bodySmall,
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildBannerPlaceholder(BuildContext context, BannerEntity banner) {
    return Container(
      color: AppColors.primary.withOpacity(0.1),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.image,
              size: 40,
              color: AppColors.primary.withOpacity(0.5),
            ),
            const ResponsiveSpacing(size: SpacingSize.sm),
            ResponsiveText(
              banner.title,
              type: TextType.bodySmall,
              color: AppColors.primary,
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingSkeleton(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, dimensions) {
        final height = dimensions.isTablet ? 160.0 : 120.0;

        return SizedBox(
          height: height,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: 3,
            itemBuilder: (context, index) {
              return Container(
                width: dimensions.screenSize.width * 0.8,
                margin: EdgeInsets.only(right: context.spacing.md),
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(16),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildOfflineIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.orange,
        borderRadius: BorderRadius.circular(8),
      ),
      child: const ResponsiveText(
        'Offline',
        type: TextType.caption,
        color: Colors.white,
      ),
    );
  }
}

// ========== RESPONSIVE ERROR CARD ==========
class ResponsiveErrorCard extends StatelessWidget {
  final String title;
  final String message;
  final VoidCallback onRetry;

  const ResponsiveErrorCard({
  super.key,
  required this.title,
  required this.message,
  required this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, dimensions) {
        return Container(
          width: double.infinity,
          padding: EdgeInsets.all(context.spacing.lg),
          decoration: BoxDecoration(
            color: Colors.red.shade50,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.red.shade200),
          ),
          child: Column(
            children: [
              Icon(
                Icons.error_outline,
                size: dimensions.isTablet ? 48 : 40,
                color: Colors.red.shade400,
              ),
              const ResponsiveSpacing(size: SpacingSize.sm),
              ResponsiveText(
                title,
                type: TextType.h4,
                color: Colors.red.shade700,
                textAlign: TextAlign.center,
              ),
              const ResponsiveSpacing(size: SpacingSize.xs),
              ResponsiveText(
                message,
                type: TextType.bodySmall,
                color: Colors.red.shade600,
                textAlign: TextAlign.center,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
              const ResponsiveSpacing(size: SpacingSize.md),
              ResponsiveButton(
                text: 'Retry',
                onPressed: onRetry,
                type: ButtonType.secondary,
                size: ButtonSize.medium,
                backgroundColor: Colors.red.shade400,
                textColor: Colors.red.shade400,
              ),
            ],
          ),
        );
      },
    );
  }
}