import 'package:freezed_annotation/freezed_annotation.dart';
part 'favorite_entity.freezed.dart';
part 'favorite_entity.g.dart';

@freezed
class FavoriteEntity with _$FavoriteEntity {
  @JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
  const factory FavoriteEntity({
    required String id,
    required String name,
    required String category,
    required String iconName,
    required String colorHex,
    required String iconUrl,
    @Default(0) int sortOrder,
    @Default(true) bool isActive,
    DateTime? lastSync,
  }) = _FavoriteEntity;
}