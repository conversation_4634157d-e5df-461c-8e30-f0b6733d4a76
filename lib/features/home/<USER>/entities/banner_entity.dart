import 'package:freezed_annotation/freezed_annotation.dart';
part 'banner_entity.freezed.dart';
part 'banner_entity.g.dart';

@freezed
class BannerEntity with _$BannerEntity {
  @JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
  const factory BannerEntity({
    required String id,
    required String title,
    required String description,
    required String imageUrl,
    String? actionUrl,
    @Default(0) int sortOrder,
    DateTime? validUntil,
    DateTime? lastSync,
  }) = _BannerEntity;

  const BannerEntity._();

  bool get isValid {
    if (validUntil == null) return true;
    return DateTime.now().isBefore(validUntil!);
  }
}