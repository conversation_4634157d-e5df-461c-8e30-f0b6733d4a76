// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'balance_entity.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$BalanceEntityImpl _$$BalanceEntityImplFromJson(Map<String, dynamic> json) =>
    _$BalanceEntityImpl(
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] as String,
      isHidden: json['is_hidden'] as bool? ?? false,
      lastUpdated: DateTime.parse(json['last_updated'] as String),
    );

Map<String, dynamic> _$$BalanceEntityImplToJson(_$BalanceEntityImpl instance) =>
    <String, dynamic>{
      'amount': instance.amount,
      'currency': instance.currency,
      'is_hidden': instance.isHidden,
      'last_updated': instance.lastUpdated.toIso8601String(),
    };
