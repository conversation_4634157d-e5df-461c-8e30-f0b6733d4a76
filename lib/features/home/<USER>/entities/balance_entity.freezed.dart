// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'balance_entity.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$BalanceEntity {
  double get amount => throw _privateConstructorUsedError;
  String get currency => throw _privateConstructorUsedError;
  bool get isHidden => throw _privateConstructorUsedError;
  DateTime get lastUpdated => throw _privateConstructorUsedError;

  /// Create a copy of BalanceEntity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BalanceEntityCopyWith<BalanceEntity> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BalanceEntityCopyWith<$Res> {
  factory $BalanceEntityCopyWith(
          BalanceEntity value, $Res Function(BalanceEntity) then) =
      _$BalanceEntityCopyWithImpl<$Res, BalanceEntity>;
  @useResult
  $Res call(
      {double amount, String currency, bool isHidden, DateTime lastUpdated});
}

/// @nodoc
class _$BalanceEntityCopyWithImpl<$Res, $Val extends BalanceEntity>
    implements $BalanceEntityCopyWith<$Res> {
  _$BalanceEntityCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BalanceEntity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = null,
    Object? currency = null,
    Object? isHidden = null,
    Object? lastUpdated = null,
  }) {
    return _then(_value.copyWith(
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      isHidden: null == isHidden
          ? _value.isHidden
          : isHidden // ignore: cast_nullable_to_non_nullable
              as bool,
      lastUpdated: null == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BalanceEntityImplCopyWith<$Res>
    implements $BalanceEntityCopyWith<$Res> {
  factory _$$BalanceEntityImplCopyWith(
          _$BalanceEntityImpl value, $Res Function(_$BalanceEntityImpl) then) =
      __$$BalanceEntityImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double amount, String currency, bool isHidden, DateTime lastUpdated});
}

/// @nodoc
class __$$BalanceEntityImplCopyWithImpl<$Res>
    extends _$BalanceEntityCopyWithImpl<$Res, _$BalanceEntityImpl>
    implements _$$BalanceEntityImplCopyWith<$Res> {
  __$$BalanceEntityImplCopyWithImpl(
      _$BalanceEntityImpl _value, $Res Function(_$BalanceEntityImpl) _then)
      : super(_value, _then);

  /// Create a copy of BalanceEntity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = null,
    Object? currency = null,
    Object? isHidden = null,
    Object? lastUpdated = null,
  }) {
    return _then(_$BalanceEntityImpl(
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      isHidden: null == isHidden
          ? _value.isHidden
          : isHidden // ignore: cast_nullable_to_non_nullable
              as bool,
      lastUpdated: null == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
class _$BalanceEntityImpl implements _BalanceEntity {
  const _$BalanceEntityImpl(
      {required this.amount,
      required this.currency,
      this.isHidden = false,
      required this.lastUpdated});

  @override
  final double amount;
  @override
  final String currency;
  @override
  @JsonKey()
  final bool isHidden;
  @override
  final DateTime lastUpdated;

  @override
  String toString() {
    return 'BalanceEntity(amount: $amount, currency: $currency, isHidden: $isHidden, lastUpdated: $lastUpdated)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BalanceEntityImpl &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.isHidden, isHidden) ||
                other.isHidden == isHidden) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, amount, currency, isHidden, lastUpdated);

  /// Create a copy of BalanceEntity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BalanceEntityImplCopyWith<_$BalanceEntityImpl> get copyWith =>
      __$$BalanceEntityImplCopyWithImpl<_$BalanceEntityImpl>(this, _$identity);
}

abstract class _BalanceEntity implements BalanceEntity {
  const factory _BalanceEntity(
      {required final double amount,
      required final String currency,
      final bool isHidden,
      required final DateTime lastUpdated}) = _$BalanceEntityImpl;

  @override
  double get amount;
  @override
  String get currency;
  @override
  bool get isHidden;
  @override
  DateTime get lastUpdated;

  /// Create a copy of BalanceEntity
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BalanceEntityImplCopyWith<_$BalanceEntityImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
