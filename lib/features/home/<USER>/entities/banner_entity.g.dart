// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'banner_entity.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$BannerEntityImpl _$$BannerEntityImplFromJson(Map<String, dynamic> json) =>
    _$BannerEntityImpl(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      imageUrl: json['image_url'] as String,
      actionUrl: json['action_url'] as String?,
      sortOrder: (json['sort_order'] as num?)?.toInt() ?? 0,
      validUntil: json['valid_until'] == null
          ? null
          : DateTime.parse(json['valid_until'] as String),
      lastSync: json['last_sync'] == null
          ? null
          : DateTime.parse(json['last_sync'] as String),
    );

Map<String, dynamic> _$$BannerEntityImplToJson(_$BannerEntityImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'image_url': instance.imageUrl,
      'action_url': instance.actionUrl,
      'sort_order': instance.sortOrder,
      'valid_until': instance.validUntil?.toIso8601String(),
      'last_sync': instance.lastSync?.toIso8601String(),
    };
