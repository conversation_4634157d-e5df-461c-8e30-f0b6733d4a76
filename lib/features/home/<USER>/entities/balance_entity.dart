import 'package:freezed_annotation/freezed_annotation.dart';

part 'balance_entity.freezed.dart';
part 'balance_entity.g.dart';

@freezed
class BalanceEntity with _$BalanceEntity {
  @JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
  const factory BalanceEntity({
    required double amount,
    required String currency,
    @Default(false) bool isHidden,
    required DateTime lastUpdated,
  }) = _BalanceEntity;
}