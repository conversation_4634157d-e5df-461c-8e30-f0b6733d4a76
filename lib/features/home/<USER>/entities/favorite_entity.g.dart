// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'favorite_entity.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$FavoriteEntityImpl _$$FavoriteEntityImplFromJson(Map<String, dynamic> json) =>
    _$FavoriteEntityImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      category: json['category'] as String,
      iconName: json['icon_name'] as String,
      colorHex: json['color_hex'] as String,
      iconUrl: json['icon_url'] as String,
      sortOrder: (json['sort_order'] as num?)?.toInt() ?? 0,
      isActive: json['is_active'] as bool? ?? true,
      lastSync: json['last_sync'] == null
          ? null
          : DateTime.parse(json['last_sync'] as String),
    );

Map<String, dynamic> _$$FavoriteEntityImplToJson(
        _$FavoriteEntityImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'category': instance.category,
      'icon_name': instance.iconName,
      'color_hex': instance.colorHex,
      'icon_url': instance.iconUrl,
      'sort_order': instance.sortOrder,
      'is_active': instance.isActive,
      'last_sync': instance.lastSync?.toIso8601String(),
    };
