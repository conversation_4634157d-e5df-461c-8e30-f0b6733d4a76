// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'banner_entity.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$BannerEntity {
  String get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  String get imageUrl => throw _privateConstructorUsedError;
  String? get actionUrl => throw _privateConstructorUsedError;
  int get sortOrder => throw _privateConstructorUsedError;
  DateTime? get validUntil => throw _privateConstructorUsedError;
  DateTime? get lastSync => throw _privateConstructorUsedError;

  /// Create a copy of BannerEntity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BannerEntityCopyWith<BannerEntity> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BannerEntityCopyWith<$Res> {
  factory $BannerEntityCopyWith(
          BannerEntity value, $Res Function(BannerEntity) then) =
      _$BannerEntityCopyWithImpl<$Res, BannerEntity>;
  @useResult
  $Res call(
      {String id,
      String title,
      String description,
      String imageUrl,
      String? actionUrl,
      int sortOrder,
      DateTime? validUntil,
      DateTime? lastSync});
}

/// @nodoc
class _$BannerEntityCopyWithImpl<$Res, $Val extends BannerEntity>
    implements $BannerEntityCopyWith<$Res> {
  _$BannerEntityCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BannerEntity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? imageUrl = null,
    Object? actionUrl = freezed,
    Object? sortOrder = null,
    Object? validUntil = freezed,
    Object? lastSync = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      imageUrl: null == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      actionUrl: freezed == actionUrl
          ? _value.actionUrl
          : actionUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      sortOrder: null == sortOrder
          ? _value.sortOrder
          : sortOrder // ignore: cast_nullable_to_non_nullable
              as int,
      validUntil: freezed == validUntil
          ? _value.validUntil
          : validUntil // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lastSync: freezed == lastSync
          ? _value.lastSync
          : lastSync // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BannerEntityImplCopyWith<$Res>
    implements $BannerEntityCopyWith<$Res> {
  factory _$$BannerEntityImplCopyWith(
          _$BannerEntityImpl value, $Res Function(_$BannerEntityImpl) then) =
      __$$BannerEntityImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String title,
      String description,
      String imageUrl,
      String? actionUrl,
      int sortOrder,
      DateTime? validUntil,
      DateTime? lastSync});
}

/// @nodoc
class __$$BannerEntityImplCopyWithImpl<$Res>
    extends _$BannerEntityCopyWithImpl<$Res, _$BannerEntityImpl>
    implements _$$BannerEntityImplCopyWith<$Res> {
  __$$BannerEntityImplCopyWithImpl(
      _$BannerEntityImpl _value, $Res Function(_$BannerEntityImpl) _then)
      : super(_value, _then);

  /// Create a copy of BannerEntity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? imageUrl = null,
    Object? actionUrl = freezed,
    Object? sortOrder = null,
    Object? validUntil = freezed,
    Object? lastSync = freezed,
  }) {
    return _then(_$BannerEntityImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      imageUrl: null == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      actionUrl: freezed == actionUrl
          ? _value.actionUrl
          : actionUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      sortOrder: null == sortOrder
          ? _value.sortOrder
          : sortOrder // ignore: cast_nullable_to_non_nullable
              as int,
      validUntil: freezed == validUntil
          ? _value.validUntil
          : validUntil // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lastSync: freezed == lastSync
          ? _value.lastSync
          : lastSync // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
class _$BannerEntityImpl extends _BannerEntity {
  const _$BannerEntityImpl(
      {required this.id,
      required this.title,
      required this.description,
      required this.imageUrl,
      this.actionUrl,
      this.sortOrder = 0,
      this.validUntil,
      this.lastSync})
      : super._();

  @override
  final String id;
  @override
  final String title;
  @override
  final String description;
  @override
  final String imageUrl;
  @override
  final String? actionUrl;
  @override
  @JsonKey()
  final int sortOrder;
  @override
  final DateTime? validUntil;
  @override
  final DateTime? lastSync;

  @override
  String toString() {
    return 'BannerEntity(id: $id, title: $title, description: $description, imageUrl: $imageUrl, actionUrl: $actionUrl, sortOrder: $sortOrder, validUntil: $validUntil, lastSync: $lastSync)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BannerEntityImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.actionUrl, actionUrl) ||
                other.actionUrl == actionUrl) &&
            (identical(other.sortOrder, sortOrder) ||
                other.sortOrder == sortOrder) &&
            (identical(other.validUntil, validUntil) ||
                other.validUntil == validUntil) &&
            (identical(other.lastSync, lastSync) ||
                other.lastSync == lastSync));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id, title, description, imageUrl,
      actionUrl, sortOrder, validUntil, lastSync);

  /// Create a copy of BannerEntity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BannerEntityImplCopyWith<_$BannerEntityImpl> get copyWith =>
      __$$BannerEntityImplCopyWithImpl<_$BannerEntityImpl>(this, _$identity);
}

abstract class _BannerEntity extends BannerEntity {
  const factory _BannerEntity(
      {required final String id,
      required final String title,
      required final String description,
      required final String imageUrl,
      final String? actionUrl,
      final int sortOrder,
      final DateTime? validUntil,
      final DateTime? lastSync}) = _$BannerEntityImpl;
  const _BannerEntity._() : super._();

  @override
  String get id;
  @override
  String get title;
  @override
  String get description;
  @override
  String get imageUrl;
  @override
  String? get actionUrl;
  @override
  int get sortOrder;
  @override
  DateTime? get validUntil;
  @override
  DateTime? get lastSync;

  /// Create a copy of BannerEntity
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BannerEntityImplCopyWith<_$BannerEntityImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
