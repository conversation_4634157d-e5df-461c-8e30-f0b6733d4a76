// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'home_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$HomeState {
// Balance section state
  BalanceEntity? get balance => throw _privateConstructorUsedError;
  bool get isBalanceLoading => throw _privateConstructorUsedError;
  String? get balanceError =>
      throw _privateConstructorUsedError; // Favorites section state
  List<FavoriteEntity> get favorites => throw _privateConstructorUsedError;
  bool get isFavoritesLoading => throw _privateConstructorUsedError;
  String? get favoritesError =>
      throw _privateConstructorUsedError; // Banners section state
  List<BannerEntity> get banners => throw _privateConstructorUsedError;
  bool get isBannersLoading => throw _privateConstructorUsedError;
  String? get bannersError =>
      throw _privateConstructorUsedError; // Global state
  bool get isOffline => throw _privateConstructorUsedError;
  int get pendingSyncCount => throw _privateConstructorUsedError;
  DateTime? get lastSyncTime => throw _privateConstructorUsedError;

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $HomeStateCopyWith<HomeState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HomeStateCopyWith<$Res> {
  factory $HomeStateCopyWith(HomeState value, $Res Function(HomeState) then) =
      _$HomeStateCopyWithImpl<$Res, HomeState>;
  @useResult
  $Res call(
      {BalanceEntity? balance,
      bool isBalanceLoading,
      String? balanceError,
      List<FavoriteEntity> favorites,
      bool isFavoritesLoading,
      String? favoritesError,
      List<BannerEntity> banners,
      bool isBannersLoading,
      String? bannersError,
      bool isOffline,
      int pendingSyncCount,
      DateTime? lastSyncTime});

  $BalanceEntityCopyWith<$Res>? get balance;
}

/// @nodoc
class _$HomeStateCopyWithImpl<$Res, $Val extends HomeState>
    implements $HomeStateCopyWith<$Res> {
  _$HomeStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? balance = freezed,
    Object? isBalanceLoading = null,
    Object? balanceError = freezed,
    Object? favorites = null,
    Object? isFavoritesLoading = null,
    Object? favoritesError = freezed,
    Object? banners = null,
    Object? isBannersLoading = null,
    Object? bannersError = freezed,
    Object? isOffline = null,
    Object? pendingSyncCount = null,
    Object? lastSyncTime = freezed,
  }) {
    return _then(_value.copyWith(
      balance: freezed == balance
          ? _value.balance
          : balance // ignore: cast_nullable_to_non_nullable
              as BalanceEntity?,
      isBalanceLoading: null == isBalanceLoading
          ? _value.isBalanceLoading
          : isBalanceLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      balanceError: freezed == balanceError
          ? _value.balanceError
          : balanceError // ignore: cast_nullable_to_non_nullable
              as String?,
      favorites: null == favorites
          ? _value.favorites
          : favorites // ignore: cast_nullable_to_non_nullable
              as List<FavoriteEntity>,
      isFavoritesLoading: null == isFavoritesLoading
          ? _value.isFavoritesLoading
          : isFavoritesLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      favoritesError: freezed == favoritesError
          ? _value.favoritesError
          : favoritesError // ignore: cast_nullable_to_non_nullable
              as String?,
      banners: null == banners
          ? _value.banners
          : banners // ignore: cast_nullable_to_non_nullable
              as List<BannerEntity>,
      isBannersLoading: null == isBannersLoading
          ? _value.isBannersLoading
          : isBannersLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      bannersError: freezed == bannersError
          ? _value.bannersError
          : bannersError // ignore: cast_nullable_to_non_nullable
              as String?,
      isOffline: null == isOffline
          ? _value.isOffline
          : isOffline // ignore: cast_nullable_to_non_nullable
              as bool,
      pendingSyncCount: null == pendingSyncCount
          ? _value.pendingSyncCount
          : pendingSyncCount // ignore: cast_nullable_to_non_nullable
              as int,
      lastSyncTime: freezed == lastSyncTime
          ? _value.lastSyncTime
          : lastSyncTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BalanceEntityCopyWith<$Res>? get balance {
    if (_value.balance == null) {
      return null;
    }

    return $BalanceEntityCopyWith<$Res>(_value.balance!, (value) {
      return _then(_value.copyWith(balance: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$HomeStateImplCopyWith<$Res>
    implements $HomeStateCopyWith<$Res> {
  factory _$$HomeStateImplCopyWith(
          _$HomeStateImpl value, $Res Function(_$HomeStateImpl) then) =
      __$$HomeStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {BalanceEntity? balance,
      bool isBalanceLoading,
      String? balanceError,
      List<FavoriteEntity> favorites,
      bool isFavoritesLoading,
      String? favoritesError,
      List<BannerEntity> banners,
      bool isBannersLoading,
      String? bannersError,
      bool isOffline,
      int pendingSyncCount,
      DateTime? lastSyncTime});

  @override
  $BalanceEntityCopyWith<$Res>? get balance;
}

/// @nodoc
class __$$HomeStateImplCopyWithImpl<$Res>
    extends _$HomeStateCopyWithImpl<$Res, _$HomeStateImpl>
    implements _$$HomeStateImplCopyWith<$Res> {
  __$$HomeStateImplCopyWithImpl(
      _$HomeStateImpl _value, $Res Function(_$HomeStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? balance = freezed,
    Object? isBalanceLoading = null,
    Object? balanceError = freezed,
    Object? favorites = null,
    Object? isFavoritesLoading = null,
    Object? favoritesError = freezed,
    Object? banners = null,
    Object? isBannersLoading = null,
    Object? bannersError = freezed,
    Object? isOffline = null,
    Object? pendingSyncCount = null,
    Object? lastSyncTime = freezed,
  }) {
    return _then(_$HomeStateImpl(
      balance: freezed == balance
          ? _value.balance
          : balance // ignore: cast_nullable_to_non_nullable
              as BalanceEntity?,
      isBalanceLoading: null == isBalanceLoading
          ? _value.isBalanceLoading
          : isBalanceLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      balanceError: freezed == balanceError
          ? _value.balanceError
          : balanceError // ignore: cast_nullable_to_non_nullable
              as String?,
      favorites: null == favorites
          ? _value._favorites
          : favorites // ignore: cast_nullable_to_non_nullable
              as List<FavoriteEntity>,
      isFavoritesLoading: null == isFavoritesLoading
          ? _value.isFavoritesLoading
          : isFavoritesLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      favoritesError: freezed == favoritesError
          ? _value.favoritesError
          : favoritesError // ignore: cast_nullable_to_non_nullable
              as String?,
      banners: null == banners
          ? _value._banners
          : banners // ignore: cast_nullable_to_non_nullable
              as List<BannerEntity>,
      isBannersLoading: null == isBannersLoading
          ? _value.isBannersLoading
          : isBannersLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      bannersError: freezed == bannersError
          ? _value.bannersError
          : bannersError // ignore: cast_nullable_to_non_nullable
              as String?,
      isOffline: null == isOffline
          ? _value.isOffline
          : isOffline // ignore: cast_nullable_to_non_nullable
              as bool,
      pendingSyncCount: null == pendingSyncCount
          ? _value.pendingSyncCount
          : pendingSyncCount // ignore: cast_nullable_to_non_nullable
              as int,
      lastSyncTime: freezed == lastSyncTime
          ? _value.lastSyncTime
          : lastSyncTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc

class _$HomeStateImpl extends _HomeState {
  const _$HomeStateImpl(
      {this.balance,
      this.isBalanceLoading = false,
      this.balanceError,
      final List<FavoriteEntity> favorites = const [],
      this.isFavoritesLoading = false,
      this.favoritesError,
      final List<BannerEntity> banners = const [],
      this.isBannersLoading = false,
      this.bannersError,
      this.isOffline = false,
      this.pendingSyncCount = 0,
      this.lastSyncTime})
      : _favorites = favorites,
        _banners = banners,
        super._();

// Balance section state
  @override
  final BalanceEntity? balance;
  @override
  @JsonKey()
  final bool isBalanceLoading;
  @override
  final String? balanceError;
// Favorites section state
  final List<FavoriteEntity> _favorites;
// Favorites section state
  @override
  @JsonKey()
  List<FavoriteEntity> get favorites {
    if (_favorites is EqualUnmodifiableListView) return _favorites;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_favorites);
  }

  @override
  @JsonKey()
  final bool isFavoritesLoading;
  @override
  final String? favoritesError;
// Banners section state
  final List<BannerEntity> _banners;
// Banners section state
  @override
  @JsonKey()
  List<BannerEntity> get banners {
    if (_banners is EqualUnmodifiableListView) return _banners;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_banners);
  }

  @override
  @JsonKey()
  final bool isBannersLoading;
  @override
  final String? bannersError;
// Global state
  @override
  @JsonKey()
  final bool isOffline;
  @override
  @JsonKey()
  final int pendingSyncCount;
  @override
  final DateTime? lastSyncTime;

  @override
  String toString() {
    return 'HomeState(balance: $balance, isBalanceLoading: $isBalanceLoading, balanceError: $balanceError, favorites: $favorites, isFavoritesLoading: $isFavoritesLoading, favoritesError: $favoritesError, banners: $banners, isBannersLoading: $isBannersLoading, bannersError: $bannersError, isOffline: $isOffline, pendingSyncCount: $pendingSyncCount, lastSyncTime: $lastSyncTime)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HomeStateImpl &&
            (identical(other.balance, balance) || other.balance == balance) &&
            (identical(other.isBalanceLoading, isBalanceLoading) ||
                other.isBalanceLoading == isBalanceLoading) &&
            (identical(other.balanceError, balanceError) ||
                other.balanceError == balanceError) &&
            const DeepCollectionEquality()
                .equals(other._favorites, _favorites) &&
            (identical(other.isFavoritesLoading, isFavoritesLoading) ||
                other.isFavoritesLoading == isFavoritesLoading) &&
            (identical(other.favoritesError, favoritesError) ||
                other.favoritesError == favoritesError) &&
            const DeepCollectionEquality().equals(other._banners, _banners) &&
            (identical(other.isBannersLoading, isBannersLoading) ||
                other.isBannersLoading == isBannersLoading) &&
            (identical(other.bannersError, bannersError) ||
                other.bannersError == bannersError) &&
            (identical(other.isOffline, isOffline) ||
                other.isOffline == isOffline) &&
            (identical(other.pendingSyncCount, pendingSyncCount) ||
                other.pendingSyncCount == pendingSyncCount) &&
            (identical(other.lastSyncTime, lastSyncTime) ||
                other.lastSyncTime == lastSyncTime));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      balance,
      isBalanceLoading,
      balanceError,
      const DeepCollectionEquality().hash(_favorites),
      isFavoritesLoading,
      favoritesError,
      const DeepCollectionEquality().hash(_banners),
      isBannersLoading,
      bannersError,
      isOffline,
      pendingSyncCount,
      lastSyncTime);

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HomeStateImplCopyWith<_$HomeStateImpl> get copyWith =>
      __$$HomeStateImplCopyWithImpl<_$HomeStateImpl>(this, _$identity);
}

abstract class _HomeState extends HomeState {
  const factory _HomeState(
      {final BalanceEntity? balance,
      final bool isBalanceLoading,
      final String? balanceError,
      final List<FavoriteEntity> favorites,
      final bool isFavoritesLoading,
      final String? favoritesError,
      final List<BannerEntity> banners,
      final bool isBannersLoading,
      final String? bannersError,
      final bool isOffline,
      final int pendingSyncCount,
      final DateTime? lastSyncTime}) = _$HomeStateImpl;
  const _HomeState._() : super._();

// Balance section state
  @override
  BalanceEntity? get balance;
  @override
  bool get isBalanceLoading;
  @override
  String? get balanceError; // Favorites section state
  @override
  List<FavoriteEntity> get favorites;
  @override
  bool get isFavoritesLoading;
  @override
  String? get favoritesError; // Banners section state
  @override
  List<BannerEntity> get banners;
  @override
  bool get isBannersLoading;
  @override
  String? get bannersError; // Global state
  @override
  bool get isOffline;
  @override
  int get pendingSyncCount;
  @override
  DateTime? get lastSyncTime;

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HomeStateImplCopyWith<_$HomeStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
