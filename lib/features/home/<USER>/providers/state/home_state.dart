import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../domain/entities/balance_entity.dart';
import '../../../domain/entities/banner_entity.dart';
import '../../../domain/entities/favorite_entity.dart';

part 'home_state.freezed.dart';

@freezed
class HomeState with _$HomeState {
  const factory HomeState({
    // Balance section state
    BalanceEntity? balance,
    @Default(false) bool isBalanceLoading,
    String? balanceError,

    // Favorites section state
    @Default([]) List<FavoriteEntity> favorites,
    @Default(false) bool isFavoritesLoading,
    String? favoritesError,

    // Banners section state
    @Default([]) List<BannerEntity> banners,
    @Default(false) bool isBannersLoading,
    String? bannersError,

    // Global state
    @Default(false) bool isOffline,
    @Default(0) int pendingSyncCount,
    DateTime? lastSyncTime,
  }) = _HomeState;

  const HomeState._();

  // Computed properties for UI
  bool get hasAnyContent => balance != null || favorites.isNotEmpty || banners.isNotEmpty;
  bool get isAnyLoading => isBalanceLoading || isFavoritesLoading || isBannersLoading;
  bool get hasAnyError => balanceError != null || favoritesError != null || bannersError != null;
}