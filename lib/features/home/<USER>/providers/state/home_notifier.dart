import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../../core/network/network_info.dart';
import '../../../../../core/sync/sync_manager.dart';
import '../../../data/repositories/home_repository.dart';
import 'home_state.dart';

class HomeNotifier extends StateNotifier<HomeState> {
  final HomeRepository _repository;
  final NetworkInfo _networkInfo;
  final SyncManager _syncManager;
  StreamSubscription<bool>? _connectivitySubscription;

  HomeNotifier(
      this._repository,
      this._networkInfo,
      this._syncManager,
      ) : super(const HomeState()) {
    _initializeConnectivity();
    loadHomeData();
  }

  void _initializeConnectivity() {
    _connectivitySubscription = _networkInfo.onConnectivityChanged.listen(
          (isConnected) {
        state = state.copyWith(isOffline: !isConnected);

        if (isConnected) {
          _syncPendingChanges();
          if (_isDataStale()) {
            loadHomeData(forceRefresh: true);
          }
        }
      },
    );
  }

  bool _isDataStale() {
    final lastSync = state.lastSyncTime;
    if (lastSync == null) return true;
    return DateTime.now().difference(lastSync).inMinutes > 15;
  }

  // ========== INDEPENDENT LOADING APPROACH ==========
  Future<void> loadHomeData({bool forceRefresh = false}) async {
    // Load each section independently for better UX
    unawaited(_loadBalance(forceRefresh: forceRefresh));
    unawaited(_loadFavorites(forceRefresh: forceRefresh));
    unawaited(_loadBanners(forceRefresh: forceRefresh));

    _updatePendingSyncCount();
  }

  Future<void> _loadBalance({bool forceRefresh = false}) async {
    if (!forceRefresh && state.isBalanceLoading) return;

    state = state.copyWith(
      isBalanceLoading: true,
      balanceError: null,
    );

    try {
      final result = await _repository.getBalance();

      result.fold(
            (error) => state = state.copyWith(
          isBalanceLoading: false,
          balanceError: error.message,
        ),
            (balance) => state = state.copyWith(
          balance: balance,
          isBalanceLoading: false,
          balanceError: null,
          lastSyncTime: DateTime.now(),
        ),
      );
    } catch (e) {
      state = state.copyWith(
        isBalanceLoading: false,
        balanceError: e.toString(),
      );
    }
  }

  Future<void> _loadFavorites({bool forceRefresh = false}) async {
    if (!forceRefresh && state.isFavoritesLoading) return;

    state = state.copyWith(
      isFavoritesLoading: true,
      favoritesError: null,
    );

    try {
      final result = await _repository.getFavorites();

      result.fold(
            (error) => state = state.copyWith(
          isFavoritesLoading: false,
          favoritesError: error.message,
        ),
            (favorites) => state = state.copyWith(
          favorites: favorites,
          isFavoritesLoading: false,
          favoritesError: null,
          lastSyncTime: DateTime.now(),
        ),
      );
    } catch (e) {
      state = state.copyWith(
        isFavoritesLoading: false,
        favoritesError: e.toString(),
      );
    }
  }

  Future<void> _loadBanners({bool forceRefresh = false}) async {
    if (!forceRefresh && state.isBannersLoading) return;

    state = state.copyWith(
      isBannersLoading: true,
      bannersError: null,
    );

    try {
      final result = await _repository.getBanners();

      result.fold(
            (error) => state = state.copyWith(
          isBannersLoading: false,
          bannersError: error.message,
        ),
            (banners) => state = state.copyWith(
          banners: banners,
          isBannersLoading: false,
          bannersError: null,
          lastSyncTime: DateTime.now(),
        ),
      );
    } catch (e) {
      state = state.copyWith(
        isBannersLoading: false,
        bannersError: e.toString(),
      );
    }
  }

  // ========== INDIVIDUAL REFRESH METHODS ==========
  Future<void> refreshBalance() async {
    await _loadBalance(forceRefresh: true);
  }

  Future<void> refreshFavorites() async {
    await _loadFavorites(forceRefresh: true);
  }

  Future<void> refreshBanners() async {
    await _loadBanners(forceRefresh: true);
  }

  Future<void> refreshAll() async {
    await Future.wait([
      _loadBalance(forceRefresh: true),
      _loadFavorites(forceRefresh: true),
      _loadBanners(forceRefresh: true),
    ]);
  }

  // ========== BUSINESS LOGIC METHODS ==========
  Future<void> toggleBalanceVisibility() async {
    if (state.balance == null) return;

    // Optimistic update
    final newBalance = state.balance!.copyWith(isHidden: !state.balance!.isHidden);
    state = state.copyWith(balance: newBalance);

    final result = await _repository.updateBalanceVisibility(newBalance.isHidden);

    result.fold(
          (error) {
        // Revert on error
        final revertedBalance = state.balance!.copyWith(isHidden: !state.balance!.isHidden);
        state = state.copyWith(
          balance: revertedBalance,
          balanceError: error.message,
        );
      },
          (_) {
        state = state.copyWith(balanceError: null);
        _updatePendingSyncCount();
      },
    );
  }

  Future<void> reorderFavorites(List<String> orderedIds) async {
    // Optimistic update
    // final reorderedFavorites = <FavoriteEntity>[];
    // for (int i = 0; i < orderedIds.length; i++) {
    //   final favorite = state.favorites.firstWhere((f) => f.id == orderedIds[i]);
    //   reorderedFavorites.add(favorite.copyWith(sortOrder: i));
    // }
    //
    // state = state.copyWith(favorites: reorderedFavorites);
    //
    // final result = await _repository.reorderFavorites(orderedIds);
    //
    // result.fold(
    //       (error) => state = state.copyWith(favoritesError: error.message),
    //       (_) {
    //     state = state.copyWith(favoritesError: null);
    //     _updatePendingSyncCount();
    //   },
    // );
  }

  Future<void> _syncPendingChanges() async {
    await _syncManager.syncPendingOperations();
    _updatePendingSyncCount();
  }

  Future<void> _updatePendingSyncCount() async {
    final pendingCount = await _syncManager.getPendingOperationsCount();
    state = state.copyWith(pendingSyncCount: pendingCount);
  }

  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    super.dispose();
  }
}
