import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/balance_entity.dart';
import '../../domain/entities/banner_entity.dart';
import '../../domain/entities/favorite_entity.dart';
import '../../domain/providers/home_provider.dart';
import 'state/home_notifier.dart';
import 'state/home_state.dart';

// ========== PRESENTATION LEVEL PROVIDERS (UI State Management) ==========

/// Main Home State Notifier Provider - Presentation layer
final homeNotifierProvider = StateNotifierProvider<HomeNotifier, HomeState>((ref) {
  final repository = ref.watch(homeRepositoryProvider);
  final networkInfo = ref.watch(networkInfoProvider);
  final syncManager = ref.watch(syncManagerProvider);

  return HomeNotifier(repository, networkInfo, syncManager);
});

/// Individual providers for specific UI needs
final balanceProvider = Provider<BalanceEntity?>((ref) {
  return ref.watch(homeNotifierProvider).balance;
});

final favoritesProvider = Provider<List<FavoriteEntity>>((ref) {
  return ref.watch(homeNotifierProvider).favorites;
});

final bannersProvider = Provider<List<BannerEntity>>((ref) {
  return ref.watch(homeNotifierProvider).banners;
});

final isOfflineProvider = Provider<bool>((ref) {
  return ref.watch(homeNotifierProvider).isOffline;
});

final pendingSyncCountProvider = Provider<int>((ref) {
  return ref.watch(homeNotifierProvider).pendingSyncCount;
});