import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/database/app_database.dart';
import '../../../../core/network/network_info.dart';
import '../../../../core/sync/sync_manager.dart';
import '../../../../shared/domain/providers/dio_network_service_provider.dart';
import '../../data/repositories/home_repository.dart';
import '../../data/datasource/home_remote_datasource.dart';
import '../../data/datasource/home_local_datasource.dart';


/// Home Repository Provider - Domain layer abstraction
final homeRepositoryProvider = Provider<HomeRepository>((ref) {
  final remoteDatasource = ref.watch(homeRemoteDatasourceProvider);
  final localDatasource = ref.watch(homeLocalDatasourceProvider);
  final networkInfo = ref.watch(networkInfoProvider);
  final syncManager = ref.watch(syncManagerProvider);

  return HomeRepositoryImpl(
    remoteDatasource,
    localDatasource,
    networkInfo,
    syncManager,
  );
});

/// Home Remote Datasource Provider
final homeRemoteDatasourceProvider = Provider<HomeRemoteDatasource>((ref) {
  final networkService = ref.watch(networkServiceProvider);
  return HomeRemoteDatasourceImpl(networkService);
});

/// Home Local Datasource Provider
final homeLocalDatasourceProvider = Provider<HomeLocalDatasource>((ref) {
  return HomeLocalDatasourceImpl();
});

/// Network Info Provider
final networkInfoProvider = Provider<NetworkInfo>((ref) {
  return NetworkInfoImpl(Connectivity());
});

/// Sync Manager Provider
final syncManagerProvider = Provider<SyncManagerImpl>((ref) {
  final networkInfo = ref.watch(networkInfoProvider);
  return SyncManagerImpl(AppDatabase as AppDatabase, networkInfo);
});
