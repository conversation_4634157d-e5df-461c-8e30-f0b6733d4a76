import 'package:freezed_annotation/freezed_annotation.dart';

import '../entities/banner_entity.dart';

part 'banner_model.freezed.dart';
part 'banner_model.g.dart';


@freezed
class BannerModel with _$BannerModel {
  @JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
  const factory BannerModel({
    required String id,
    required String title,
    required String description,
    required String imageUrl,
    String? actionUrl,
    @Default(0) int sortOrder,
    String? validUntil,
    String? lastSync,
  }) = _BannerModel;

  const BannerModel._();

  bool get isValid {
    if (validUntil == null) return true;
    return DateTime.now().isBefore(validUntil! as DateTime);
  }

  factory BannerModel.fromJson(Map<String, dynamic> json) =>
      _$BannerModelFromJson(json);

  BannerEntity toEntity() => BannerEntity(
    id: id,
    title: title,
    description: description,
    imageUrl: imageUrl,
    actionUrl: actionUrl,
    sortOrder: sortOrder,
    validUntil: validUntil != null ? DateTime.parse(validUntil!) : null,
    lastSync: lastSync != null ? DateTime.parse(lastSync!) : null,
  );

  factory BannerModel.fromEntity(BannerEntity entity) => BannerModel(
    id: entity.id,
    title: entity.title,
    description: entity.description,
    imageUrl: entity.imageUrl,
    actionUrl: entity.actionUrl,
    sortOrder: entity.sortOrder,
    validUntil: entity.validUntil?.toIso8601String(),
    lastSync: entity.lastSync?.toIso8601String(),
  );
}