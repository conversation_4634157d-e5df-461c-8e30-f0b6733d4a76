import 'package:freezed_annotation/freezed_annotation.dart';

import '../entities/favorite_entity.dart';

part 'favorite_model.freezed.dart';
part 'favorite_model.g.dart';

@freezed
class FavoriteModel with _$FavoriteModel {
  @JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
  const factory FavoriteModel({
    required String id,
    required String name,
    required String category,
    required String iconName,
    required String iconUrl,
    required String colorHex,
    @Default(0) int sortOrder,
    @Default(true) bool isActive,
    String? lastSync,
  }) = _FavoriteModel;

  factory FavoriteModel.fromJson(Map<String, dynamic> json) =>
      _$FavoriteModelFromJson(json);

  FavoriteEntity toEntity() => FavoriteEntity(
        id: id,
        name: name,
        category: category,
        iconName: iconName,
        iconUrl: iconUrl,
        colorHex: colorHex,
        sortOrder: sortOrder,
        isActive: isActive,
        lastSync: lastSync != null ? DateTime.parse(lastSync!) : null,
      );

  factory FavoriteModel.fromEntity(FavoriteEntity entity) => FavoriteModel(
        id: entity.id,
        name: entity.name,
        category: entity.category,
        iconName: entity.iconName,
        iconUrl: entity.iconUrl,
        colorHex: entity.colorHex,
        sortOrder: entity.sortOrder,
        isActive: entity.isActive,
        lastSync: entity.lastSync?.toIso8601String(),
      );
}
