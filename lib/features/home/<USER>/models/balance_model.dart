import 'package:freezed_annotation/freezed_annotation.dart';

import '../entities/balance_entity.dart';

part 'balance_model.freezed.dart';
part 'balance_model.g.dart';

@freezed
class BalanceModel with _$BalanceModel {
  @JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
  const factory BalanceModel({
    required double amount,
    required String currency,
    @Default(false) bool isHidden,
    required String lastUpdated,
  }) = _BalanceModel;

  factory BalanceModel.fromJson(Map<String, dynamic> json) =>
      _$BalanceModelFromJson(json);

  const BalanceModel._();

  BalanceEntity toEntity() => BalanceEntity(
    amount: amount,
    currency: currency,
    isHidden: isHidden,
    lastUpdated: DateTime.parse(lastUpdated),
  );
}