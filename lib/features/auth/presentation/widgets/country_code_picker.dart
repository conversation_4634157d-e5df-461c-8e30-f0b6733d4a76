import 'package:flutter/material.dart';
import '../../../../shared/theme/app_colors.dart';

class CountryCodePicker extends StatelessWidget {
  final String selectedCountryCode;
  final Function(String) onChanged;

  const CountryCodePicker({
  super.key,
  required this.selectedCountryCode,
  required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _showCountryPicker(context),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 24,
              height: 16,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(2),
                color: Colors.red,
              ),
              child: const Center(
                child: Text(
                  '🇮🇩',
                  style: TextStyle(fontSize: 12),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Text(
              selectedCountryCode,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(width: 4),
            const Icon(
              Icons.keyboard_arrow_down,
              size: 20,
              color: AppColors.textSecondary,
            ),
          ],
        ),
      ),
    );
  }

  void _showCountryPicker(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => CountryPickerBottomSheet(
        selectedCountryCode: selectedCountryCode,
        onChanged: onChanged,
      ),
    );
  }
}

class CountryPickerBottomSheet extends StatelessWidget {
  final String selectedCountryCode;
  final Function(String) onChanged;

  const CountryPickerBottomSheet({
  super.key,
  required this.selectedCountryCode,
  required this.onChanged,
  });

  final List<CountryData> countries = const [
    CountryData(name: 'Indonesia', code: '+62', flag: '🇮🇩'),
    CountryData(name: 'Malaysia', code: '+60', flag: '🇲🇾'),
    CountryData(name: 'Singapore', code: '+65', flag: '🇸🇬'),
    CountryData(name: 'Thailand', code: '+66', flag: '🇹🇭'),
    CountryData(name: 'Philippines', code: '+63', flag: '🇵🇭'),
    CountryData(name: 'Vietnam', code: '+84', flag: '🇻🇳'),
    CountryData(name: 'United States', code: '+1', flag: '🇺🇸'),
    CountryData(name: 'United Kingdom', code: '+44', flag: '🇬🇧'),
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 20),
          const Text(
            'Select Country',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          Flexible(
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: countries.length,
              itemBuilder: (context, index) {
                final country = countries[index];
                final isSelected = country.code == selectedCountryCode;

                return ListTile(
                  leading: Text(
                    country.flag,
                    style: const TextStyle(fontSize: 24),
                  ),
                  title: Text(country.name),
                  trailing: Text(
                    country.code,
                    style: TextStyle(
                      color: isSelected ? AppColors.primary : AppColors.textSecondary,
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                  selected: isSelected,
                  selectedTileColor: AppColors.primary.withOpacity(0.1),
                  onTap: () {
                    onChanged(country.code);
                    Navigator.of(context).pop();
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

class CountryData {
  final String name;
  final String code;
  final String flag;

  const CountryData({
    required this.name,
    required this.code,
    required this.flag,
  });
}