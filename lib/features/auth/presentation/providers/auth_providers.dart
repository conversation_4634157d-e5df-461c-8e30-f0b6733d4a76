import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_qubli/features/auth/presentation/providers/state/auth_notifier.dart';
import 'package:mobile_qubli/features/auth/presentation/providers/state/auth_state.dart';

import '../../../../shared/domain/models/auth/auth_model.dart';
import '../../domain/providers/auth_providers.dart';

// Main Auth Provider
final authNotifierProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  final authRepository = ref.watch(authRepositoryProvider);
  return AuthNotifier(authRepository);
});

// Computed Providers
final currentUserProvider = Provider<AuthModel?>((ref) {
  final authState = ref.watch(authNotifierProvider);
  return authState.maybeWhen(
    authenticated: (user) => user,
    orElse: () => null,
  );
});

final isAuthenticatedProvider = Provider<bool>((ref) {
  final authState = ref.watch(authNotifierProvider);
  return authState.maybeWhen(
    authenticated: (_) => true,
    orElse: () => false,
  );
});

final isLoadingProvider = Provider<bool>((ref) {
  final authState = ref.watch(authNotifierProvider);
  return authState.maybeWhen(
    loading: () => true,
    orElse: () => false,
  );
});

final authErrorProvider = Provider<String?>((ref) {
  final authState = ref.watch(authNotifierProvider);
  return authState.maybeWhen(
    error: (exception) => exception.message,
    orElse: () => null,
  );
});

// Form State Providers
class LoginFormState {
  final String phoneNumber;
  final String countryCode;
  final bool isValid;

  const LoginFormState({
    this.phoneNumber = '',
    this.countryCode = '+62',
    this.isValid = false,
  });

  LoginFormState copyWith({
    String? phoneNumber,
    String? countryCode,
    bool? isValid,
  }) {
    return LoginFormState(
      phoneNumber: phoneNumber ?? this.phoneNumber,
      countryCode: countryCode ?? this.countryCode,
      isValid: isValid ?? this.isValid,
    );
  }
}

class LoginFormNotifier extends StateNotifier<LoginFormState> {
  LoginFormNotifier() : super(const LoginFormState());

  void updatePhoneNumber(String phoneNumber) {
    state = state.copyWith(
      phoneNumber: phoneNumber,
      isValid: _validateForm(phoneNumber, state.countryCode),
    );
  }

  void updateCountryCode(String countryCode) {
    state = state.copyWith(
      countryCode: countryCode,
      isValid: _validateForm(state.phoneNumber, countryCode),
    );
  }

  bool _validateForm(String phoneNumber, String countryCode) {
    return phoneNumber.trim().isNotEmpty &&
        phoneNumber.length >= 8 &&
        countryCode.isNotEmpty;
  }

  void reset() {
    state = const LoginFormState();
  }
}

final loginFormProvider = StateNotifierProvider<LoginFormNotifier, LoginFormState>((ref) {
  return LoginFormNotifier();
});

// Register Form State
class RegisterFormState {
  final String name;
  final String phoneNumber;
  final String countryCode;
  final String referralCode;
  final bool agreedToTerms;
  final bool isValid;

  const RegisterFormState({
    this.name = '',
    this.phoneNumber = '',
    this.countryCode = '+62',
    this.referralCode = '',
    this.agreedToTerms = false,
    this.isValid = false,
  });

  RegisterFormState copyWith({
    String? name,
    String? phoneNumber,
    String? countryCode,
    String? referralCode,
    bool? agreedToTerms,
    bool? isValid,
  }) {
    return RegisterFormState(
      name: name ?? this.name,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      countryCode: countryCode ?? this.countryCode,
      referralCode: referralCode ?? this.referralCode,
      agreedToTerms: agreedToTerms ?? this.agreedToTerms,
      isValid: isValid ?? this.isValid,
    );
  }
}

class RegisterFormNotifier extends StateNotifier<RegisterFormState> {
  RegisterFormNotifier() : super(const RegisterFormState());

  void updateName(String name) {
    state = state.copyWith(
      name: name,
      isValid: _validateForm(name, state.phoneNumber, state.agreedToTerms),
    );
  }

  void updatePhoneNumber(String phoneNumber) {
    state = state.copyWith(
      phoneNumber: phoneNumber,
      isValid: _validateForm(state.name, phoneNumber, state.agreedToTerms),
    );
  }

  void updateCountryCode(String countryCode) {
    state = state.copyWith(countryCode: countryCode);
  }

  void updateReferralCode(String referralCode) {
    state = state.copyWith(referralCode: referralCode);
  }

  void updateAgreedToTerms(bool agreed) {
    state = state.copyWith(
      agreedToTerms: agreed,
      isValid: _validateForm(state.name, state.phoneNumber, agreed),
    );
  }

  bool _validateForm(String name, String phoneNumber, bool agreedToTerms) {
    return name.trim().isNotEmpty &&
        phoneNumber.trim().isNotEmpty &&
        phoneNumber.length >= 8 &&
        agreedToTerms;
  }

  void reset() {
    state = const RegisterFormState();
  }
}

final registerFormProvider = StateNotifierProvider<RegisterFormNotifier, RegisterFormState>((ref) {
  return RegisterFormNotifier();
});