import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../../../shared/domain/models/auth/auth_model.dart';
import '../../../../../shared/exceptions/http_exception.dart';


part 'auth_state.freezed.dart';

@freezed
class AuthState with _$AuthState {
  const factory AuthState.initial() = _Initial;
  const factory AuthState.loading() = _Loading;
  const factory AuthState.authenticated(AuthModel user) = _Authenticated;
  const factory AuthState.unauthenticated() = _Unauthenticated;
  const factory AuthState.error(AppException exception) = _Error;
}