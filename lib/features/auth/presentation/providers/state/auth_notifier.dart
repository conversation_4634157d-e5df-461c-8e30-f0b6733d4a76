import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../data/repositories/auth_repository.dart';
import 'auth_state.dart';

class AuthNotifier extends StateNotifier<AuthState> {
  final AuthRepository _authRepository;

  AuthNotifier(this._authRepository) : super(const AuthState.initial()) {
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    final result = await _authRepository.isAuthenticated();
    result.fold(
          (failure) => state = const AuthState.unauthenticated(),
          (isAuthenticated) async {
        if (isAuthenticated) {
          final userResult = await _authRepository.getCurrentUser();
          userResult.fold(
                (failure) => state = const AuthState.unauthenticated(),
                (user) => state = user != null
                ? AuthState.authenticated(user)
                : const AuthState.unauthenticated(),
          );
        } else {
          state = const AuthState.unauthenticated();
        }
      },
    );
  }

  Future<void> login({
    required String phoneNumber,
    required String countryCode,
  }) async {
    state = const AuthState.loading();

    final result = await _authRepository.login(
      phoneNumber: phoneNumber,
      countryCode: countryCode,
    );

    result.fold(
          (failure) => state = AuthState.error(failure),
          (user) => state = AuthState.authenticated(user),
    );
  }

  Future<void> register({
    required String name,
    required String phoneNumber,
    required String countryCode,
    String? referralCode,
  }) async {
    state = const AuthState.loading();

    final result = await _authRepository.register(
      name: name,
      phoneNumber: phoneNumber,
      countryCode: countryCode,
      referralCode: referralCode,
    );

    result.fold(
          (failure) => state = AuthState.error(failure),
          (user) => state = AuthState.authenticated(user),
    );
  }

  Future<void> logout() async {
    state = const AuthState.loading();

    final result = await _authRepository.logout();

    result.fold(
          (failure) => state = const AuthState.unauthenticated(), // Still logout locally
          (_) => state = const AuthState.unauthenticated(),
    );
  }

  void clearError() {
    if (state is Error) {
      state = const AuthState.unauthenticated();
    }
  }
}
