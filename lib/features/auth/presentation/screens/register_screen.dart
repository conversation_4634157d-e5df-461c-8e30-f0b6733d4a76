import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:auto_route/auto_route.dart';
import '../../../../routes/app_route.dart';
import '../../../../shared/theme/app_colors.dart';
import '../../../../shared/widgets/loading_overlay.dart';
import '../../../../shared/widgets/responsive_widget.dart';
import '../../../../shared/utils/responsive_utils.dart';
import '../providers/auth_providers.dart';
import '../providers/state/auth_state.dart';

@RoutePage()
class RegisterScreen extends ConsumerStatefulWidget {
  const RegisterScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends ConsumerState<RegisterScreen> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _referralController = TextEditingController();

  final FocusNode _nameFocus = FocusNode();
  final FocusNode _phoneFocus = FocusNode();
  final FocusNode _referralFocus = FocusNode();

  @override
  void initState() {
    super.initState();

    // Delay provider setup until after first frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        // Listen to form changes and update providers
        _nameController.addListener(() {
          ref
              .read(registerFormProvider.notifier)
              .updateName(_nameController.text);
        });
        _phoneController.addListener(() {
          ref
              .read(registerFormProvider.notifier)
              .updatePhoneNumber(_phoneController.text);
        });
        _referralController.addListener(() {
          ref
              .read(registerFormProvider.notifier)
              .updateReferralCode(_referralController.text);
        });
      } catch (e) {
        print('Error setting up form listeners: $e');
      }
    });
  }

  String _selectedCountryCode = '+62';
  bool _agreeToTerms = false;

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _referralController.dispose();
    _nameFocus.dispose();
    _phoneFocus.dispose();
    _referralFocus.dispose();
    super.dispose();
  }

  void _handleRegister() async {
    final formState = ref.read(registerFormProvider);
    if (!formState.isValid) {
      String errorMessage = 'Silakan lengkapi semua field yang diperlukan';
      if (formState.name.trim().isEmpty) {
        errorMessage = 'Silakan masukkan nama lengkap';
      } else if (formState.phoneNumber.trim().isEmpty) {
        errorMessage = 'Silakan masukkan nomor HP';
      } else if (!formState.agreedToTerms) {
        errorMessage =
            'Anda harus menyetujui kebijakan privasi dan ketentuan kami';
      }

      _showError(errorMessage);
      return;
    }

    // Call register using auth notifier
    await ref.read(authNotifierProvider.notifier).register(
          name: formState.name,
          phoneNumber: formState.phoneNumber,
          countryCode: formState.countryCode,
          referralCode:
              formState.referralCode.isNotEmpty ? formState.referralCode : null,
        );
  }

  bool _validateForm() {
    // This is now handled by the provider
    return ref.read(registerFormProvider).isValid;
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: ResponsiveText(
          message,
          type: TextType.bodyMedium,
          color: Colors.white,
        ),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Listen to auth state changes
    ref.listen<AuthState>(authNotifierProvider, (previous, next) {
      next.maybeWhen(
        authenticated: (user) {
          // Navigate to home after successful registration
          // context.router.push(const HomeRoute());
        },
        error: (exception) {
          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: ResponsiveText(
                exception.message,
                type: TextType.bodyMedium,
                color: Colors.white,
              ),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
        },
        orElse: () {},
      );
    });

    // final isLoading = ref.watch(isLoadingProvider);
    bool isLoading = false;
    try {
      isLoading = ref.watch(isLoadingProvider);
    } catch (e) {
      print('Loading provider not ready: $e');
      isLoading = false;
    }

    return LoadingOverlay(
        isLoading: isLoading,
        loadingText: 'Mendaftarkan akun...',
        child: Scaffold(
            body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.white,
                Colors.grey.shade50,
                Colors.grey.shade100,
              ],
              stops: const [0.0, 0.7, 1.0],
            ),
          ),
          child: SafeArea(
            child: Column(
              children: [
                Expanded(
                  child: ResponsiveLayout(
                    mobile: _buildMobileLayout(),
                    tablet: _buildTabletLayout(),
                    desktop: _buildDesktopLayout(),
                  ),
                ),
              ],
            ),
          ),
        )));
  }

  Widget _buildMobileLayout() {
    return SingleChildScrollView(
      child: ResponsiveContainer(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const ResponsiveSpacing(size: SpacingSize.md),
            _buildHeader(),
            const ResponsiveSpacing(size: SpacingSize.xl),
            _buildFormFields(),
            const ResponsiveSpacing(size: SpacingSize.lg),
            _buildTermsCheckbox(),
            const ResponsiveSpacing(size: SpacingSize.xl),
            _buildRegisterButton(),
            const ResponsiveSpacing(size: SpacingSize.md),
            _buildLoginPrompt(),
            const ResponsiveSpacing(size: SpacingSize.lg),
          ],
        ),
      ),
    );
  }

  Widget _buildTabletLayout() {
    return Row(
      children: [
        Expanded(flex: 1, child: Container()),
        Expanded(
          flex: 2,
          child: SingleChildScrollView(
            child: ResponsiveContainer(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const ResponsiveSpacing(size: SpacingSize.lg),
                  _buildHeader(),
                  const ResponsiveSpacing(size: SpacingSize.xl),
                  _buildFormFields(),
                  const ResponsiveSpacing(size: SpacingSize.lg),
                  _buildTermsCheckbox(),
                  const ResponsiveSpacing(size: SpacingSize.xl),
                  _buildRegisterButton(),
                  const ResponsiveSpacing(size: SpacingSize.md),
                  _buildLoginPrompt(),
                  const ResponsiveSpacing(size: SpacingSize.lg),
                ],
              ),
            ),
          ),
        ),
        Expanded(flex: 1, child: Container()),
      ],
    );
  }

  Widget _buildDesktopLayout() {
    return Row(
      children: [
        Expanded(flex: 2, child: Container()),
        Expanded(
          flex: 3,
          child: SingleChildScrollView(
            child: ResponsiveContainer(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const ResponsiveSpacing(size: SpacingSize.xl),
                  _buildHeader(),
                  const ResponsiveSpacing(size: SpacingSize.xl),
                  _buildFormFields(),
                  const ResponsiveSpacing(size: SpacingSize.lg),
                  _buildTermsCheckbox(),
                  const ResponsiveSpacing(size: SpacingSize.xl),
                  _buildRegisterButton(),
                  const ResponsiveSpacing(size: SpacingSize.md),
                  _buildLoginPrompt(),
                  const ResponsiveSpacing(size: SpacingSize.xl),
                ],
              ),
            ),
          ),
        ),
        Expanded(flex: 2, child: Container()),
      ],
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const ResponsiveText(
          'Daftar',
          type: TextType.h2,
          fontWeight: FontWeight.bold,
        ),
        const ResponsiveSpacing(size: SpacingSize.xs),
        const ResponsiveText(
          'Isi dan Lengkapi data di bawah ini untuk melanjutkan!',
          type: TextType.bodyMedium,
          color: AppColors.textSecondary,
        ),
      ],
    );
  }

  Widget _buildFormFields() {
    return Column(
      children: [
        _buildNameField(),
        const ResponsiveSpacing(size: SpacingSize.lg),
        _buildPhoneField(),
        const ResponsiveSpacing(size: SpacingSize.lg),
        _buildReferralField(),
      ],
    );
  }

  Widget _buildNameField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const ResponsiveText(
          'Nama Lengkap *',
          type: TextType.bodyMedium,
          fontWeight: FontWeight.w500,
        ),
        const ResponsiveSpacing(size: SpacingSize.xs),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: _nameFocus.hasFocus
                  ? AppColors.primary
                  : Colors.grey.shade200,
              width: _nameFocus.hasFocus ? 2 : 1,
            ),
            boxShadow: [
              BoxShadow(
                color: _nameFocus.hasFocus
                    ? AppColors.primary.withOpacity(0.15)
                    : Colors.black.withOpacity(0.08),
                blurRadius: _nameFocus.hasFocus ? 12 : 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: TextField(
            controller: _nameController,
            focusNode: _nameFocus,
            style: TextStyle(
              fontSize: context.typography.bodyMedium,
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w500,
            ),
            decoration: InputDecoration(
              hintText: 'Janet Waluyo',
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: context.responsive.horizontalPadding * 0.67,
                vertical: context.responsive.isSmallScreen ? 16.0 : 18.0,
              ),
              hintStyle: TextStyle(
                color: Colors.grey.shade400,
                fontSize: context.typography.bodyMedium,
                fontWeight: FontWeight.normal,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPhoneField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const ResponsiveText(
          'Nomor HP *',
          type: TextType.bodyMedium,
          fontWeight: FontWeight.w500,
        ),
        const ResponsiveSpacing(size: SpacingSize.xs),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: _phoneFocus.hasFocus
                  ? AppColors.primary
                  : Colors.grey.shade200,
              width: _phoneFocus.hasFocus ? 2 : 1,
            ),
            boxShadow: [
              BoxShadow(
                color: _phoneFocus.hasFocus
                    ? AppColors.primary.withOpacity(0.15)
                    : Colors.black.withOpacity(0.08),
                blurRadius: _phoneFocus.hasFocus ? 12 : 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: IntrinsicHeight(
            child: Row(
              children: [
                // Country code picker
                GestureDetector(
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: ResponsiveText(
                          'Pemilihan negara akan tersedia segera',
                          type: TextType.bodyMedium,
                          color: Colors.white,
                        ),
                        backgroundColor: AppColors.primary,
                        behavior: SnackBarBehavior.floating,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    );
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: context.responsive.horizontalPadding * 0.67,
                      vertical: context.responsive.isSmallScreen ? 16.0 : 18.0,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(16),
                        bottomLeft: Radius.circular(16),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // const ResponsiveText('🇮🇩', type: TextType.bodyLarge),
                        // const ResponsiveSpacing(
                        //   size: SpacingSize.xs,
                        //   direction: Axis.horizontal,
                        // ),
                        ResponsiveText(
                          _selectedCountryCode,
                          type: TextType.bodyMedium,
                          fontWeight: FontWeight.w600,
                        ),
                        const ResponsiveSpacing(
                          size: SpacingSize.xs,
                          direction: Axis.horizontal,
                        ),
                        Icon(
                          Icons.keyboard_arrow_down,
                          size: 18,
                          color: Colors.grey.shade600,
                        ),
                      ],
                    ),
                  ),
                ),
                // Phone number input
                Expanded(
                  child: TextField(
                    controller: _phoneController,
                    focusNode: _phoneFocus,
                    keyboardType: TextInputType.phone,
                    style: TextStyle(
                      fontSize: context.typography.bodyMedium,
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.w500,
                    ),
                    decoration: InputDecoration(
                      hintText: '8123456789',
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: context.responsive.horizontalPadding * 0.67,
                        vertical:
                            context.responsive.isSmallScreen ? 16.0 : 18.0,
                      ),
                      hintStyle: TextStyle(
                        color: Colors.grey.shade400,
                        fontSize: context.typography.bodyMedium,
                        fontWeight: FontWeight.normal,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildReferralField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const ResponsiveText(
          'Kode Referral (Opsional)',
          type: TextType.bodyMedium,
          fontWeight: FontWeight.w500,
        ),
        const ResponsiveSpacing(size: SpacingSize.xs),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: _referralFocus.hasFocus
                  ? AppColors.primary
                  : Colors.grey.shade200,
              width: _referralFocus.hasFocus ? 2 : 1,
            ),
            boxShadow: [
              BoxShadow(
                color: _referralFocus.hasFocus
                    ? AppColors.primary.withOpacity(0.15)
                    : Colors.black.withOpacity(0.08),
                blurRadius: _referralFocus.hasFocus ? 12 : 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: TextField(
            controller: _referralController,
            focusNode: _referralFocus,
            style: TextStyle(
              fontSize: context.typography.bodyMedium,
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w500,
            ),
            decoration: InputDecoration(
              hintText: 'Masukkan Kode Referral',
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: context.responsive.horizontalPadding * 0.67,
                vertical: context.responsive.isSmallScreen ? 16.0 : 18.0,
              ),
              hintStyle: TextStyle(
                color: Colors.grey.shade400,
                fontSize: context.typography.bodyMedium,
                fontWeight: FontWeight.normal,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTermsCheckbox() {
    // Get form state with fallback
    RegisterFormState formState;
    try {
      formState = ref.watch(registerFormProvider);
    } catch (e) {
      // Fallback form state if provider not ready
      formState = const RegisterFormState();
    }
    return GestureDetector(
      onTap: () {
        setState(() {
          _agreeToTerms = !_agreeToTerms;
          ref
              .read(registerFormProvider.notifier)
              .updateAgreedToTerms(!formState.agreedToTerms);
        });
      },
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 20,
            height: 20,
            margin: const EdgeInsets.only(top: 2),
            decoration: BoxDecoration(
              color: _agreeToTerms ? AppColors.primary : Colors.white,
              border: Border.all(
                color: _agreeToTerms ? AppColors.primary : Colors.grey.shade300,
                width: 2,
              ),
              borderRadius: BorderRadius.circular(4),
            ),
            child: _agreeToTerms
                ? const Icon(
                    Icons.check,
                    size: 14,
                    color: Colors.white,
                  )
                : null,
          ),
          const ResponsiveSpacing(
            size: SpacingSize.sm,
            direction: Axis.horizontal,
          ),
          Expanded(
            child: RichText(
              text: TextSpan(
                style: TextStyle(
                  fontSize: context.typography.bodyMedium,
                  color: Colors.grey.shade600,
                  height: 1.4,
                ),
                children: [
                  const TextSpan(
                      text: 'Dengan masuk atau daftar, Anda menyetujui '),
                  TextSpan(
                    text: 'Kebijakan Privasi',
                    style: TextStyle(
                      color: AppColors.primary,
                      fontWeight: FontWeight.w600,
                      decoration: TextDecoration.underline,
                    ),
                  ),
                  const TextSpan(text: ' dan '),
                  TextSpan(
                    text: 'Ketentuan kami',
                    style: TextStyle(
                      color: AppColors.primary,
                      fontWeight: FontWeight.w600,
                      decoration: TextDecoration.underline,
                    ),
                  ),
                  const TextSpan(text: '.'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRegisterButton() {
    // Get loading state with fallback
    bool isLoading = false;
    try {
      isLoading = ref.watch(isLoadingProvider);
    } catch (e) {
      isLoading = false;
    }

    // Get form state with fallback
    RegisterFormState formState;
    try {
      formState = ref.watch(registerFormProvider);
    } catch (e) {
      // Fallback form state if provider not ready
      formState = const RegisterFormState();
    }

    return SizedBox(
      width: double.infinity,
      child: ResponsiveButton(
        text: 'Daftar Sekarang',
        type: ButtonType.primary,
        size: ButtonSize.large,
        isLoading: isLoading,
        onPressed: (isLoading || !formState.isValid) ? null : _handleRegister,
      ),
    );
  }

  // NEW: Login prompt section
  Widget _buildLoginPrompt() {
    return Center(
      child: Column(
        children: [
          // Divider with "atau" text
          Row(
            children: [
              Expanded(
                child: Container(
                  height: 1,
                  color: Colors.grey.shade300,
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: ResponsiveText(
                  'atau',
                  type: TextType.bodySmall,
                  color: Colors.grey.shade500,
                ),
              ),
              Expanded(
                child: Container(
                  height: 1,
                  color: Colors.grey.shade300,
                ),
              ),
            ],
          ),

          const ResponsiveSpacing(size: SpacingSize.md),

          // Login prompt text and button
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ResponsiveText(
                'Sudah punya akun?',
                type: TextType.bodyMedium,
                color: Colors.grey.shade600,
              ),
              const ResponsiveSpacing(
                size: SpacingSize.xs,
                direction: Axis.horizontal,
              ),
              GestureDetector(
                onTap: () => context.router.push(const LoginRoute()),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.transparent,
                  ),
                  child: ResponsiveText(
                    'Masuk Sekarang',
                    type: TextType.bodyMedium,
                    color: AppColors.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
