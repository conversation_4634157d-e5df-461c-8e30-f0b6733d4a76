import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:auto_route/auto_route.dart';
import '../../../../routes/app_route.dart';
import '../../../../shared/theme/app_colors.dart';
import '../../../../shared/widgets/responsive_widget.dart';
import '../../../../shared/utils/responsive_utils.dart';
import '../providers/auth_providers.dart';

@RoutePage()
class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<LoginScreen> createState() => _LoginRouteState();
}

class _LoginRouteState extends ConsumerState<LoginScreen> {
  final TextEditingController _phoneController = TextEditingController();
  final FocusNode _phoneFocus = FocusNode();
  String _selectedCountryCode = '+62';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();

    // Delay provider setup until after first frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        // Listen to form changes and update providers
        _phoneController.addListener(() {
          ref
              .read(loginFormProvider.notifier)
              .updatePhoneNumber(_phoneController.text);
        });
      } catch (e) {
        print('Error setting up form listeners: $e');
      }
    });
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _phoneFocus.dispose();
    super.dispose();
  }

  void _showUnregisteredDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        contentPadding: EdgeInsets.zero,
        content: Container(
          padding: EdgeInsets.all(context.responsive.horizontalPadding),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const ResponsiveSpacing(size: SpacingSize.sm),

              // Title
              const ResponsiveText(
                'Nomor Belum Terdaftar',
                type: TextType.h3,
                fontWeight: FontWeight.bold,
              ),

              const ResponsiveSpacing(size: SpacingSize.md),

              // Content
              const ResponsiveText(
                'Buat akun baru dan nikmati kemudahan transaksi digital produk digital bersama kami.',
                type: TextType.bodyMedium,
                color: AppColors.textSecondary,
              ),

              const ResponsiveSpacing(size: SpacingSize.lg),

              // Buttons
              Column(
                children: [
                  // Back button (Secondary)
                  SizedBox(
                    width: double.infinity,
                    child: ResponsiveButton(
                      text: 'Kembali',
                      type: ButtonType.secondary,
                      size: ButtonSize.medium,
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ),

                  const ResponsiveSpacing(size: SpacingSize.sm),

                  // Register button (Primary)
                  SizedBox(
                    width: double.infinity,
                    child: ResponsiveButton(
                      text: 'Buat Akun',
                      type: ButtonType.primary,
                      size: ButtonSize.medium,
                      onPressed: () {
                        Navigator.of(context).pop();
                        context.router.push(const RegisterRoute());
                      },
                    ),
                  ),
                ],
              ),

              const ResponsiveSpacing(size: SpacingSize.sm),
            ],
          ),
        ),
      ),
    );
  }

  void _handleLogin() async {
    final formState = ref.read(loginFormProvider);

    if (_phoneController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: ResponsiveText(
            'Silakan masukkan nomor HP',
            type: TextType.bodyMedium,
            color: Colors.white,
          ),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );
      return;
    }

    void _showError(String message) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: ResponsiveText(
            message,
            type: TextType.bodyMedium,
            color: Colors.white,
          ),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Simulate login process
      await Future.delayed(const Duration(seconds: 1));
      if (mounted) {
        _showUnregisteredDialog();
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.white,
              Colors.grey.shade50,
              Colors.grey.shade100,
            ],
            stops: const [0.0, 0.7, 1.0],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // _buildAppBar(),
              Expanded(
                child: ResponsiveLayout(
                  mobile: _buildMobileLayout(),
                  tablet: _buildTabletLayout(),
                  desktop: _buildDesktopLayout(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Container(
      height: kToolbarHeight,
      padding: EdgeInsets.symmetric(
        horizontal: context.responsive.horizontalPadding,
      ),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => context.router.maybePop(),
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Icon(
                Icons.arrow_back,
                color: Colors.black87,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMobileLayout() {
    return ResponsiveContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const ResponsiveSpacing(size: SpacingSize.xl),
          _buildHeader(),
          const ResponsiveSpacing(size: SpacingSize.xl),
          _buildPhoneField(),
          const Spacer(),
          _buildLoginButton(),
          const ResponsiveSpacing(size: SpacingSize.md),
          _buildRegisterPrompt(),
          const ResponsiveSpacing(size: SpacingSize.lg),
        ],
      ),
    );
  }

  Widget _buildTabletLayout() {
    return Row(
      children: [
        Expanded(
          flex: 1,
          child: Container(), // Empty space for centering
        ),
        Expanded(
          flex: 2,
          child: ResponsiveContainer(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const ResponsiveSpacing(size: SpacingSize.lg),
                _buildHeader(),
                const ResponsiveSpacing(size: SpacingSize.xl),
                _buildPhoneField(),
                const Spacer(),
                _buildLoginButton(),
                const ResponsiveSpacing(size: SpacingSize.md),
                _buildRegisterPrompt(),
                const ResponsiveSpacing(size: SpacingSize.lg),
              ],
            ),
          ),
        ),
        Expanded(
          flex: 1,
          child: Container(), // Empty space for centering
        ),
      ],
    );
  }

  Widget _buildDesktopLayout() {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: Container(), // Empty space for centering
        ),
        Expanded(
          flex: 3,
          child: ResponsiveContainer(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const ResponsiveSpacing(size: SpacingSize.xl),
                _buildHeader(),
                const ResponsiveSpacing(size: SpacingSize.xl),
                _buildPhoneField(),
                const Spacer(),
                _buildLoginButton(),
                const ResponsiveSpacing(size: SpacingSize.md),
                _buildRegisterPrompt(),
                const ResponsiveSpacing(size: SpacingSize.xl),
              ],
            ),
          ),
        ),
        Expanded(
          flex: 2,
          child: Container(), // Empty space for centering
        ),
      ],
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const ResponsiveText(
          'Masuk',
          type: TextType.h2,
          fontWeight: FontWeight.bold,
        ),
        const ResponsiveSpacing(size: SpacingSize.xs),
        const ResponsiveText(
          'Silakan masukkan nomor HP Anda untuk melanjutkan',
          type: TextType.bodyMedium,
          color: AppColors.textSecondary,
        ),
      ],
    );
  }

  Widget _buildPhoneField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const ResponsiveText(
          'Nomor HP',
          type: TextType.bodyMedium,
          fontWeight: FontWeight.w500,
        ),
        const ResponsiveSpacing(size: SpacingSize.xs),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: _phoneFocus.hasFocus
                  ? AppColors.primary
                  : Colors.grey.shade200,
              width: _phoneFocus.hasFocus ? 2 : 1,
            ),
            boxShadow: [
              BoxShadow(
                color: _phoneFocus.hasFocus
                    ? AppColors.primary.withOpacity(0.15)
                    : Colors.black.withOpacity(0.08),
                blurRadius: _phoneFocus.hasFocus ? 12 : 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: IntrinsicHeight(
            child: Row(
              children: [
                // Country code picker
                GestureDetector(
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: ResponsiveText(
                          'Pemilihan negara akan tersedia segera',
                          type: TextType.bodyMedium,
                          color: Colors.white,
                        ),
                        backgroundColor: AppColors.primary,
                        behavior: SnackBarBehavior.floating,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    );
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: context.responsive.horizontalPadding * 0.67,
                      vertical: context.responsive.isSmallScreen ? 16.0 : 18.0,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(16),
                        bottomLeft: Radius.circular(16),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // const ResponsiveText('🇮🇩', type: TextType.bodyLarge),
                        // const ResponsiveSpacing(
                        //   size: SpacingSize.xs,
                        //   direction: Axis.horizontal,
                        // ),
                        ResponsiveText(
                          _selectedCountryCode,
                          type: TextType.bodyMedium,
                          fontWeight: FontWeight.w600,
                        ),
                        const ResponsiveSpacing(
                          size: SpacingSize.xs,
                          direction: Axis.horizontal,
                        ),
                        Icon(
                          Icons.keyboard_arrow_down,
                          size: 18,
                          color: Colors.grey.shade600,
                        ),
                      ],
                    ),
                  ),
                ),

                // Phone number input
                Expanded(
                  child: TextField(
                    controller: _phoneController,
                    focusNode: _phoneFocus,
                    keyboardType: TextInputType.phone,
                    style: TextStyle(
                      fontSize: context.typography.bodyMedium,
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.w500,
                    ),
                    decoration: InputDecoration(
                      hintText: '8123456789',
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: context.responsive.horizontalPadding * 0.67,
                        vertical: context.responsive.isSmallScreen ? 16.0 : 18.0,
                      ),
                      hintStyle: TextStyle(
                        color: Colors.grey.shade400,
                        fontSize: context.typography.bodyMedium,
                        fontWeight: FontWeight.normal,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLoginButton() {
    return SizedBox(
      width: double.infinity,
      child: ResponsiveButton(
        text: 'Masuk',
        type: ButtonType.primary,
        size: ButtonSize.large,
        isLoading: _isLoading,
        onPressed: _isLoading ? null : _handleLogin,
      ),
    );
  }

  Widget _buildRegisterPrompt() {
    return Center(
      child: Column(
        children: [
          // Divider with "atau" text
          Row(
            children: [
              Expanded(
                child: Container(
                  height: 1,
                  color: Colors.grey.shade300,
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: ResponsiveText(
                  'atau',
                  type: TextType.bodySmall,
                  color: Colors.grey.shade500,
                ),
              ),
              Expanded(
                child: Container(
                  height: 1,
                  color: Colors.grey.shade300,
                ),
              ),
            ],
          ),

          const ResponsiveSpacing(size: SpacingSize.md),

          // Register prompt text and button
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ResponsiveText(
                'Belum punya akun?',
                type: TextType.bodyMedium,
                color: Colors.grey.shade600,
              ),
              const ResponsiveSpacing(
                size: SpacingSize.xs,
                direction: Axis.horizontal,
              ),
              GestureDetector(
                onTap: () => context.router.push(const RegisterRoute()),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.transparent,
                  ),
                  child: ResponsiveText(
                    'Daftar Sekarang',
                    type: TextType.bodyMedium,
                    color: AppColors.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}