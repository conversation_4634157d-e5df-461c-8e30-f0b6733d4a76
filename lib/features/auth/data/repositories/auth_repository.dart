import 'package:dartz/dartz.dart';
import '../../../../shared/domain/models/auth/auth_model.dart';
import '../../../../shared/exceptions/http_exception.dart';
import '../datasource/auth_local_data_source.dart';
import '../datasource/auth_remote_data_source.dart';

abstract class AuthRepository {
  Future<Either<AppException, AuthModel>> login({
    required String phoneNumber,
    required String countryCode,
  });

  Future<Either<AppException, AuthModel>> register({
    required String name,
    required String phoneNumber,
    required String countryCode,
    String? referralCode,
  });

  Future<Either<AppException, void>> logout();
  Future<Either<AppException, AuthModel?>> getCurrentUser();
  Future<Either<AppException, bool>> isAuthenticated();
  Future<Either<AppException, void>> refreshToken();
}

class AuthenticationRepositoryImpl implements AuthRepository {
  final AuthRemoteDataSource _remoteDataSource;
  final AuthLocalDataSource _localDataSource;

  AuthenticationRepositoryImpl(
      this._remoteDataSource,
      this._localDataSource,
      );

  @override
  Future<Either<AppException, AuthModel>> login({
    required String phoneNumber,
    required String countryCode,
  }) async {
    final result = await _remoteDataSource.login(
      phoneNumber: phoneNumber,
      countryCode: countryCode,
    );

    return result.fold(
          (failure) => Left(failure),
          (authData) async {
        // authData is now directly AuthData, not a response wrapper

        // Save tokens
        final tokenResult = await _localDataSource.saveTokens(
          accessToken: authData.accessToken,
          refreshToken: authData.refreshToken,
        );

        if (tokenResult.isLeft()) {
          return const Left(AppException.unknown('Failed to save authentication data'));
        }

        // Save user data
        final userResult = await _localDataSource.saveUser(authData);

        return userResult.fold(
              (failure) => Left(AppException.unknown('Failed to save user data')),
              (_) => Right(authData),
        );
      },
    );
  }

  @override
  Future<Either<AppException, AuthModel>> register({
    required String name,
    required String phoneNumber,
    required String countryCode,
    String? referralCode,
  }) async {
    final result = await _remoteDataSource.register(
      name: name,
      phoneNumber: phoneNumber,
      countryCode: countryCode,
      referralCode: referralCode,
    );

    return result.fold(
          (failure) => Left(failure),
          (authData) async {
        // authData is now directly AuthData, not a response wrapper

        // Save tokens
        final tokenResult = await _localDataSource.saveTokens(
          accessToken: authData.accessToken,
          refreshToken: authData.refreshToken,
        );

        if (tokenResult.isLeft()) {
          return Left(AppException.unknown('Failed to save authentication data'));
        }

        // Save user data
        final userResult = await _localDataSource.saveUser(authData);

        return userResult.fold(
              (failure) => Left(AppException.unknown('Failed to save user data')),
              (_) => Right(authData),
        );
      },
    );
  }

  @override
  Future<Either<AppException, void>> logout() async {
    // Call remote logout (optional - continue even if fails)
    await _remoteDataSource.logout();

    // Clear local data (this is the important part)
    return await _localDataSource.clearAuthData();
  }

  @override
  Future<Either<AppException, AuthModel?>> getCurrentUser() async {
    return await _localDataSource.getUser();
  }

  @override
  Future<Either<AppException, bool>> isAuthenticated() async {
    final tokenResult = await _localDataSource.getAccessToken();
    return tokenResult.fold(
          (failure) => const Right(false),
          (token) => Right(token != null && token.isNotEmpty),
    );
  }

  @override
  Future<Either<AppException, void>> refreshToken() async {
    final result = await _remoteDataSource.refreshToken();

    return result.fold(
          (failure) => Left(failure),
          (authData) async {
        // authData is now directly AuthData from refresh response

        final tokenResult = await _localDataSource.saveTokens(
          accessToken: authData.accessToken,
          refreshToken: authData.refreshToken,
        );

        return tokenResult.fold(
              (failure) => Left(AppException.unknown('Failed to save refreshed tokens')),
              (_) => const Right(null),
        );
      },
    );
  }
}