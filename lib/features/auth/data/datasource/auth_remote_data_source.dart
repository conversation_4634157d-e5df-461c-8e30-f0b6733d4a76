import 'package:dartz/dartz.dart';
import '../../../../shared/data/remote/network_service.dart';
import '../../../../shared/domain/models/auth/auth_model.dart';
import '../../../../shared/domain/models/response.dart';
import '../../../../shared/exceptions/http_exception.dart';

abstract class AuthRemoteDataSource {
  Future<Either<AppException, AuthModel>> login({
    required String phoneNumber,
    required String countryCode,
  });

  Future<Either<AppException, AuthModel>> register({
    required String name,
    required String phoneNumber,
    required String countryCode,
    String? referralCode,
  });

  Future<Either<AppException, void>> logout();
  Future<Either<AppException, AuthModel>> refreshToken();
}

class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final NetworkService _networkService;

  AuthRemoteDataSourceImpl(this._networkService);

  @override
  Future<Either<AppException, AuthModel>> login({
    required String phoneNumber,
    required String countryCode,
  }) async {
    final result = await _networkService.post('/auth/login', data: {
      'phone_number': phoneNumber,
      'country_code': countryCode,
    });

    return result.fold(
          (failure) => Left(failure),
          (response) {
        try {
          // Cast dynamic to Map<String, dynamic>
          final apiResponse = ApiResponse.fromJson(response as Map<String, dynamic>);
          // EXACTLY WHAT YOU WANT - Direct JSON parsing
          return apiResponse.parseData<AuthModel>((data) => AuthModel.fromJson(data));

        } catch (e) {
          return Left(AppException.unknown('Failed to parse login response: ${e.toString()}'));
        }
      },
    );
  }

  @override
  Future<Either<AppException, AuthModel>> register({
    required String name,
    required String phoneNumber,
    required String countryCode,
    String? referralCode,
  }) async {
    final result = await _networkService.post('/auth/register', data: {
      'name': name,
      'phone_number': phoneNumber,
      'country_code': countryCode,
      if (referralCode != null && referralCode.isNotEmpty)
        'referral_code': referralCode,
    });

    return result.fold(
          (failure) => Left(failure),
          (response) {
        try {
          // Cast dynamic to Map<String, dynamic>
          final apiResponse = ApiResponse.fromJson(response as Map<String, dynamic>);
          // EXACTLY WHAT YOU WANT - Direct JSON parsing
          return apiResponse.parseData<AuthModel>((data) => AuthModel.fromJson(data));
        } catch (e) {
          return Left(AppException.unknown('Failed to parse register response: ${e.toString()}'));
        }
      },
    );
  }

  @override
  Future<Either<AppException, void>> logout() async {
    final result = await _networkService.post('/auth/logout');

    return result.fold(
          (failure) => Left(failure),
          (response) {
        try {
          final apiResponse = ApiResponse.fromJson(
              response as Map<String, dynamic>);

          if (apiResponse.isSuccess) {
            return const Right(null);
          } else {
            return Left(apiResponse.mapStatusCodeToException());
          }
        } catch (e) {
          return Left(
              AppException.unknown('Failed to parse logout response: $e'));
        }
      },
    );
  }

  @override
  Future<Either<AppException, AuthModel>> refreshToken() async {
    final result = await _networkService.post('/auth/refresh');

    return result.fold(
          (failure) => Left(failure),
          (response) {
        try {
          // Cast dynamic to Map<String, dynamic>
          final apiResponse = ApiResponse.fromJson(response as Map<String, dynamic>);
          // EXACTLY WHAT YOU WANT - Direct JSON parsing
          return apiResponse.parseData<AuthModel>((data) => AuthModel.fromJson(data));
        } catch (e) {
          return Left(AppException.unknown('Failed to parse refresh token response: ${e.toString()}'));
        }
      },
    );
  }
}