import 'package:dartz/dartz.dart';
import '../../../../shared/data/local/storage_service.dart';
import '../../../../shared/domain/models/auth/auth_model.dart';
import '../../../../shared/exceptions/http_exception.dart';
import '../../../../shared/mixins/exception_handler_mixin.dart';

abstract class AuthLocalDataSource {
  Future<Either<AppException, void>> saveUser(AuthModel user);
  Future<Either<AppException, AuthModel?>> getUser();
  Future<Either<AppException, void>> saveTokens({
    required String accessToken,
    String? refreshToken,
  });
  Future<Either<AppException, String?>> getAccessToken();
  Future<Either<AppException, String?>> getRefreshToken();
  Future<Either<AppException, void>> clearAuthData();
}

class AuthLocalDataSourceImpl extends AuthLocalDataSource with ExceptionHandlerMixin {
  final StorageService _storageService;

  static const String _userKey = 'user_data';
  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';

  AuthLocalDataSourceImpl(this._storageService);

  @override
  Future<Either<AppException, void>> saveUser(AuthModel user) {
    return handleException(() async {
      await _storageService.save(_userKey, user.toJson());
    });
  }

  @override
  Future<Either<AppException, AuthModel?>> getUser() {
    return handleException(() async {
      final userData = await _storageService.get(_userKey);
      if (userData != null && userData is Map<String, dynamic>) {
        return AuthModel.fromJson(userData);
      }
      return null;
    });
  }

  @override
  Future<Either<AppException, void>> saveTokens({
    required String accessToken,
    String? refreshToken,
  }) {
    return handleException(() async {
      await _storageService.save(_accessTokenKey, accessToken);
      if (refreshToken != null) {
        await _storageService.save(_refreshTokenKey, refreshToken);
      }
    });
  }

  @override
  Future<Either<AppException, String?>> getAccessToken() {
    return handleException(() async {
      return await _storageService.get(_accessTokenKey) as String?;
    });
  }

  @override
  Future<Either<AppException, String?>> getRefreshToken() {
    return handleException(() async {
      return await _storageService.get(_refreshTokenKey) as String?;
    });
  }

  @override
  Future<Either<AppException, void>> clearAuthData() {
    return handleException(() async {
      await Future.wait([
        _storageService.remove(_userKey),
        _storageService.remove(_accessTokenKey),
        _storageService.remove(_refreshTokenKey),
      ]);
    });
  }
}