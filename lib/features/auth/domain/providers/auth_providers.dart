import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/repositories/auth_repository.dart';
import '../../data/datasource/auth_remote_data_source.dart';
import '../../data/datasource/auth_local_data_source.dart';
import '../../../../shared/domain/providers/dio_network_service_provider.dart';
import '../../../../shared/domain/providers/shared_preferences_storage_service_provider.dart';

// Data Source Providers
final authRemoteDataSourceProvider = Provider<AuthRemoteDataSource>((ref) {
  final networkService = ref.watch(networkServiceProvider);
  return AuthRemoteDataSourceImpl(networkService);
});

final authLocalDataSourceProvider = Provider<AuthLocalDataSource>((ref) {
  final storageService = ref.watch(storageServiceProvider);
  return AuthLocalDataSourceImpl(storageService);
});

// Repository Provider
final authRepositoryProvider = Provider<AuthRepository>((ref) {
  final remoteDataSource = ref.watch(authRemoteDataSourceProvider);
  final localDataSource = ref.watch(authLocalDataSourceProvider);
  return AuthenticationRepositoryImpl(remoteDataSource, localDataSource);
});