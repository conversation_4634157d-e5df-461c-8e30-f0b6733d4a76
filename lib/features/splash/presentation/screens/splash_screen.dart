import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:auto_route/auto_route.dart';
import '../../../../../../routes/app_route.dart';
import '../../../../../../shared/theme/app_colors.dart';
import '../../../../../../shared/utils/responsive_utils.dart';
import '../../../../shared/widgets/responsive_widget.dart';

@RoutePage()
class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  int _currentPage = 0;
  bool _isNavigating = false;

  final List<OnboardingData> _onboardingData = [
    OnboardingData(
      title: "Mudah Bertransaksi",
      description: "Nikmati kemudahan bertransaksi dengan aplikasi kami. Proses yang cepat dan aman untuk semua kebutuhan digital Anda.",
      imagePath: "assets/images/onboarding_1.png",
      icon: Icons.shopping_bag_outlined,
      color: Colors.blue,
    ),
    OnboardingData(
      title: "Aman & Terpercaya",
      description: "Keamanan data dan transaksi Anda adalah prioritas utama kami. Teknologi enkripsi terdepan melindungi setiap aktivitas.",
      imagePath: "assets/images/onboarding_2.png",
      icon: Icons.security_outlined,
      color: Colors.green,
    ),
    OnboardingData(
      title: "Mudah Bertransaksi",
      description: "Akses global dengan jangkauan luas. Transaksi internasional menjadi lebih mudah dan efisien dari mana saja.",
      imagePath: "assets/images/onboarding_3.png",
      icon: Icons.public_outlined,
      color: Colors.orange,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _startAnimations();
  }

  void _initializeControllers() {
    _pageController = PageController();

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));
  }

  void _startAnimations() {
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        _fadeController.forward();
        _slideController.forward();
      }
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  void _nextPage() async {
    if (_isNavigating) return;
    HapticFeedback.lightImpact();

    if (_currentPage < _onboardingData.length - 1) {
      await _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _navigateToLogin();
    }
  }

  void _skipToRegister() async {
    if (_isNavigating) return;
    HapticFeedback.lightImpact();
    _navigateToRegister();
  }

  void _navigateToLogin() async {
    setState(() {
      _isNavigating = true;
    });
    await _fadeController.reverse();
    if (mounted) {
      context.router.navigate(const LoginRoute());
    }
  }

  void _navigateToRegister() async {
    setState(() {
      _isNavigating = true;
    });
    await _fadeController.reverse();
    if (mounted) {
      context.router.navigate(const RegisterRoute());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: ResponsiveLayout(
              mobile: _buildMobileLayout(),
              tablet: _buildTabletLayout(),
              desktop: _buildDesktopLayout(),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMobileLayout() {
    return Column(
      children: [
        _buildSkipButton(),
        Expanded(
          child: PageView.builder(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentPage = index;
              });
              HapticFeedback.selectionClick();
            },
            itemCount: _onboardingData.length,
            itemBuilder: (context, index) {
              return ResponsiveOnboardingPage(
                data: _onboardingData[index],
              );
            },
          ),
        ),
        _buildBottomSection(),
      ],
    );
  }

  Widget _buildTabletLayout() {
    return Row(
      children: [
        Expanded(
          flex: 6,
          child: PageView.builder(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentPage = index;
              });
              HapticFeedback.selectionClick();
            },
            itemCount: _onboardingData.length,
            itemBuilder: (context, index) {
              return ResponsiveOnboardingPage(
                data: _onboardingData[index],
              );
            },
          ),
        ),
        Expanded(
          flex: 4,
          child: ResponsiveContainer(
            useVerticalPadding: true,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildPageIndicators(),
                const ResponsiveSpacing(size: SpacingSize.xl),
                _buildNavigationButtons(),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDesktopLayout() {
    return Row(
      children: [
        Expanded(
          flex: 7,
          child: PageView.builder(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentPage = index;
              });
              HapticFeedback.selectionClick();
            },
            itemCount: _onboardingData.length,
            itemBuilder: (context, index) {
              return ResponsiveOnboardingPage(
                data: _onboardingData[index],
              );
            },
          ),
        ),
        Expanded(
          flex: 3,
          child: ResponsiveContainer(
            useVerticalPadding: true,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildPageIndicators(),
                const ResponsiveSpacing(size: SpacingSize.xl),
                _buildNavigationButtons(),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSkipButton() {
    return Padding(
      padding: EdgeInsets.all(context.responsive.horizontalPadding),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          TextButton(
            onPressed: _isNavigating ? null : () => _navigateToLogin(),
            child: ResponsiveText(
              'Lewati',
              type: TextType.bodyMedium,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomSection() {
    return ResponsiveContainer(
      useVerticalPadding: true,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildPageIndicators(),
          const ResponsiveSpacing(size: SpacingSize.lg),
          _buildNavigationButtons(),
        ],
      ),
    );
  }

  Widget _buildPageIndicators() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        _onboardingData.length,
            (index) => AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          margin: const EdgeInsets.symmetric(horizontal: 4),
          height: 8,
          width: _currentPage == index ? 24 : 8,
          decoration: BoxDecoration(
            color: _currentPage == index
                ? AppColors.primary
                : AppColors.primary.withOpacity(0.3),
            borderRadius: BorderRadius.circular(4),
          ),
        ),
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Column(
      children: [
        // Wrap ResponsiveButton in SizedBox for full width
        SizedBox(
          width: double.infinity,
          child: ResponsiveButton(
            text: _currentPage == _onboardingData.length - 1 ? "Masuk" : "Lanjut",
            type: ButtonType.primary,
            isLoading: _isNavigating,
            onPressed: _isNavigating ? null : _nextPage,
          ),
        ),
        const ResponsiveSpacing(size: SpacingSize.sm),
        SizedBox(
          width: double.infinity,
          child: ResponsiveButton(
            text: "Daftar",
            type: ButtonType.secondary,
            onPressed: _isNavigating ? null : _skipToRegister,
          ),
        ),
      ],
    );
  }
}

// FIXED: Updated ResponsiveOnboardingPage to prevent overflow
class ResponsiveOnboardingPage extends StatelessWidget {
  final OnboardingData data;

  const ResponsiveOnboardingPage({
  super.key,
  required this.data,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveContainer(
      child: LayoutBuilder(
        builder: (context, constraints) {
          final availableHeight = constraints.maxHeight;
          final isVeryTightSpace = availableHeight < 400;
          final isTightSpace = availableHeight < 500;

          return SingleChildScrollView(
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: constraints.maxHeight,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Image with responsive sizing
                  ResponsiveImage(
                    icon: data.icon,
                    iconColor: data.color.withOpacity(0.8),
                    size: isVeryTightSpace
                        ? ImageSize.small
                        : isTightSpace
                        ? ImageSize.medium
                        : ImageSize.large,
                    backgroundColor: data.color.withOpacity(0.1),
                  ),

                  ResponsiveSpacing(
                    size: isVeryTightSpace
                        ? SpacingSize.sm
                        : isTightSpace
                        ? SpacingSize.md
                        : SpacingSize.lg,
                  ),

                  // Title with responsive sizing
                  ResponsiveText(
                    data.title,
                    type: isVeryTightSpace
                        ? TextType.h4
                        : isTightSpace
                        ? TextType.h3
                        : TextType.h2,
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  ResponsiveSpacing(
                    size: isVeryTightSpace
                        ? SpacingSize.xs
                        : SpacingSize.sm,
                  ),

                  // Description with responsive sizing
                  Container(
                    constraints: BoxConstraints(
                      maxWidth: context.responsive.isLandscape ? 400 : double.infinity,
                    ),
                    child: ResponsiveText(
                      data.description,
                      type: isVeryTightSpace
                          ? TextType.bodySmall
                          : TextType.bodyMedium,
                      color: AppColors.textSecondary,
                      textAlign: TextAlign.center,
                      maxLines: isVeryTightSpace
                          ? 2
                          : isTightSpace
                          ? 3
                          : 4,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

class OnboardingData {
  final String title;
  final String description;
  final String imagePath;
  final IconData icon;
  final Color color;

  OnboardingData({
    required this.title,
    required this.description,
    required this.imagePath,
    required this.icon,
    required this.color,
  });
}