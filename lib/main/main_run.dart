import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../configs/env.dart';
import '../routes/app_route.dart';
import '../shared/domain/providers/shared_preferences_storage_service_provider.dart';
import 'observer.dart';

Future<void> mainRun() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize dependencies
  print('Initializing SharedPreferences...');
  // Initialize dependencies
  final sharedPreferences = await SharedPreferences.getInstance();
  print('SharedPreferences initialized successfully');

  print('Starting app with ProviderScope...');
  runApp(
    ProviderScope(
      observers: [
        if (Env.isDevelopment) AppProviderObserver(),
      ],
      overrides: [
        sharedPreferencesProvider.overrideWithValue(sharedPreferences),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final appRouter = AppRouter();

    return MaterialApp.router(
      title: _getAppTitle(),
      routerConfig: appRouter.config(),
      debugShowCheckedModeBanner: Env.isDevelopment,
    );
  }

  String _getAppTitle() {
    switch (Env.environment) {
      case Environment.development:
        return '${AppConfigs.appName} (Dev)';
      case Environment.staging:
        return '${AppConfigs.appName} (Staging)';
      case Environment.production:
        return AppConfigs.appName;
    }
  }
}