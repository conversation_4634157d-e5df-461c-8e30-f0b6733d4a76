import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../features/auth/presentation/providers/auth_providers.dart';
import '../features/auth/presentation/providers/state/auth_state.dart';
import '../routes/app_route.dart';
import '../shared/theme/app_theme.dart';
import '../configs/app_configs.dart';

class MyApp extends ConsumerWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appRouter = AppRouter();

    // Listen to authentication state changes
    ref.listen<AuthState>(authNotifierProvider, (previous, next) {
      next.when(
        initial: () {},
        loading: () {},
        authenticated: (user) {
          // Navigate to authenticated screens
          // appRouter.pushAndClearStack(const DashboardRoute());
        },
        unauthenticated: () {
          // Navigate to unauthenticated screens
          appRouter.replaceAll([const SplashRoute()]);
        },
        error: (exception) {
          // Handle auth errors - could show global error handling here
          debugPrint('Auth Error: ${exception.message}');
        },
      );
    });

    return MaterialApp.router(
      title: AppConfigs.appName,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      routerConfig: appRouter.config(),
      debugShowCheckedModeBanner: AppConfigs.isDevelopment,
    );
  }
}