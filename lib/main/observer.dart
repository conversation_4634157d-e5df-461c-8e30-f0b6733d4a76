import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';

class AppProviderObserver extends ProviderObserver {
  @override
  void didAddProvider(
      ProviderBase<Object?> provider,
      Object? value,
      ProviderContainer container,
      ) {
    if (kDebugMode) {
      print('[Provider] Added: ${provider.name ?? provider.runtimeType} = $value');
    }
  }

  @override
  void didDisposeProvider(
      ProviderBase<Object?> provider,
      ProviderContainer container,
      ) {
    if (kDebugMode) {
      print('[Provider] Disposed: ${provider.name ?? provider.runtimeType}');
    }
  }

  @override
  void didUpdateProvider(
      ProviderBase<Object?> provider,
      Object? previousValue,
      Object? newValue,
      ProviderContainer container,
      ) {
    if (kDebugMode) {
      print('[Provider] Updated: ${provider.name ?? provider.runtimeType} '
          'from $previousValue to $newValue');
    }
  }

  @override
  void providerDidFail(
      ProviderBase<Object?> provider,
      Object error,
      StackTrace stackTrace,
      ProviderContainer container,
      ) {
    if (kDebugMode) {
      print('[Provider] Error in ${provider.name ?? provider.runtimeType}: $error');
      print('Stack trace: $stackTrace');
    }
  }
}