enum AppEnvironment { development, staging, production }

class AppEnv {
  static AppEnvironment get current {
    const environment = String.fromEnvironment('ENVIRONMENT', defaultValue: 'development');

    switch (environment.toLowerCase()) {
      case 'staging':
        return AppEnvironment.staging;
      case 'production':
        return AppEnvironment.production;
      default:
        return AppEnvironment.development;
    }
  }

  static bool get isDevelopment => current == AppEnvironment.development;
  static bool get isStaging => current == AppEnvironment.staging;
  static bool get isProduction => current == AppEnvironment.production;
}