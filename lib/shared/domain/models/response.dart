import 'package:dartz/dartz.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../exceptions/http_exception.dart';

part 'response.freezed.dart';

part 'response.g.dart';

@Freezed(genericArgumentFactories: true)
class ApiResponse with _$ApiResponse {
  const factory ApiResponse({
    required bool success,
    required String message,
    required int status_code,
    dynamic data,
    Map<String, dynamic>? meta,
    List<String>? errors,
  }) = _ApiResponse;

  factory ApiResponse.fromJson(Map<String, dynamic> json) =>
      _$ApiResponseFromJson(json);
}

extension ApiResponseX on ApiResponse {
  bool get isSuccess => status_code >= 200 && status_code < 300;

  bool get isError => !isSuccess;

  // Direct access to data with type casting
  T? getData<T>() => data as T?;

  // Convert to Either with custom parsing
  Either<AppException, T> parseData<T>(
      T Function(Map<String, dynamic>) from<PERSON>son) {
    if (isSuccess && data != null) {
      try {
        final dataMap = data as Map<String, dynamic>;
        final parsed = fromJson(dataMap);
        return Right(parsed);
      } catch (e) {
        return Left(AppException.unknown('Failed to parse data: $e'));
      }
    } else {
      return Left(mapStatusCodeToException());
    }
  }

  AppException mapStatusCodeToException() {
    switch (status_code) {
      case 400:
        return AppException.badRequest(message);
      case 401:
        return AppException.unauthorized(message);
      case 403:
        return AppException.forbidden(message);
      case 404:
        return AppException.notFound(message);
      case 422:
        return AppException.validation(message);
      case 500:
      case 502:
      case 503:
      case 504:
        return AppException.server(message);
      default:
        return AppException.unknown('HTTP $status_code: $message');
    }
  }
}
