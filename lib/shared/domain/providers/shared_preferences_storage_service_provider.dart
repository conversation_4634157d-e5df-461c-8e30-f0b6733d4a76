import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../data/local/storage_service.dart';
import '../../data/local/shared_prefs_storage_service.dart';

final sharedPreferencesProvider = Provider<SharedPreferences>((ref) {
  throw UnimplementedError('SharedPreferences must be overridden in main.dart');
});

final storageServiceProvider = Provider<StorageService>((ref) {
  final sharedPrefs = ref.watch(sharedPreferencesProvider);
  return SharedPrefsStorageService(sharedPrefs);
});

// lib/configs/app_configs.dartç
class AppConfigs {
  static const String baseUrl = String.fromEnvironment(
    'BASE_URL',
    defaultValue: 'https://api.yourapp.com/v1',
  );

  static const Duration connectionTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);

  static const String appName = 'Your App Name';
  static const String appVersion = '1.0.0';

  // Environment flags
  static const bool isDevelopment = bool.fromEnvironment('DEVELOPMENT', defaultValue: true);
  static const bool enableLogging = bool.fromEnvironment('ENABLE_LOGGING', defaultValue: true);

  // API Configuration
  static const String apiKey = String.fromEnvironment('API_KEY', defaultValue: '');
  static const int pageSize = 20;

  // Cache Configuration
  static const Duration cacheExpiration = Duration(hours: 24);

  // Security
  static const int tokenRefreshThreshold = 300; // 5 minutes in seconds
}