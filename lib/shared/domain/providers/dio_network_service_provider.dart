import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/remote/network_service.dart';
import '../../data/remote/dio_network_service.dart';
import 'shared_preferences_storage_service_provider.dart';

final dioProvider = Provider<Dio>((ref) {
  return Dio(BaseOptions(
    baseUrl: AppConfigs.baseUrl,
    connectTimeout: AppConfigs.connectionTimeout,
    receiveTimeout: AppConfigs.receiveTimeout,
    sendTimeout: AppConfigs.sendTimeout,
  ));
});

final networkServiceProvider = Provider<NetworkService>((ref) {
  final dio = ref.watch(dioProvider);
  final storageService = ref.watch(storageServiceProvider);
  return DioNetworkService(dio, storageService);
});