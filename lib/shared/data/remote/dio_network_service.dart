import 'package:dio/dio.dart';
import 'package:dartz/dartz.dart';
import '../../exceptions/http_exception.dart';
import '../local/storage_service.dart';
import 'network_service.dart';

class DioNetworkService implements NetworkService {
  final Dio _dio;
  final StorageService _storageService;

  DioNetworkService(this._dio, this._storageService) {
    _setupInterceptors();
  }

  @override
  String get baseUrl => _dio.options.baseUrl;

  @override
  Map<String, String> get headers => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  void _setupInterceptors() {
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          // Add auth token
          final token = await _storageService.get('auth_token');
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
          }
          handler.next(options);
        },
        onError: (error, handler) async {
          if (error.response?.statusCode == 401) {
            // Token expired, try refresh
            await _handleTokenRefresh();
          }
          handler.next(error);
        },
      ),
    );

    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      logPrint: (object) => print('[Network] $object'),
    ));
  }

  Future<void> _handleTokenRefresh() async {
    try {
      final refreshToken = await _storageService.get('refresh_token');
      if (refreshToken != null) {
        final response = await _dio.post('/auth/refresh', data: {
          'refresh_token': refreshToken,
        });

        final newToken = response.data['token'];
        await _storageService.save('auth_token', newToken);
      }
    } catch (e) {
      await _storageService.remove('auth_token');
      await _storageService.remove('refresh_token');
    }
  }

  @override
  Future<Either<AppException, dynamic>> get(
      String endpoint, {
        Map<String, dynamic>? queryParameters,
      }) async {
    try {
      final response = await _dio.get(
        endpoint,
        queryParameters: queryParameters,
      );
      return Right(response.data);
    } on DioException catch (e) {
      return Left(_handleDioError(e));
    } catch (e) {
      return Left(AppException.unknown(e.toString()));
    }
  }

  @override
  Future<Either<AppException, dynamic>> post(
      String endpoint, {
        Map<String, dynamic>? data,
      }) async {
    try {
      final response = await _dio.post(endpoint, data: data);
      return Right(response.data);
    } on DioException catch (e) {
      return Left(_handleDioError(e));
    } catch (e) {
      return Left(AppException.unknown(e.toString()));
    }
  }

  @override
  Future<Either<AppException, dynamic>> put(
      String endpoint, {
        Map<String, dynamic>? data,
      }) async {
    try {
      final response = await _dio.put(endpoint, data: data);
      return Right(response.data);
    } on DioException catch (e) {
      return Left(_handleDioError(e));
    } catch (e) {
      return Left(AppException.unknown(e.toString()));
    }
  }

  @override
  Future<Either<AppException, dynamic>> delete(String endpoint) async {
    try {
      final response = await _dio.delete(endpoint);
      return Right(response.data);
    } on DioException catch (e) {
      return Left(_handleDioError(e));
    } catch (e) {
      return Left(AppException.unknown(e.toString()));
    }
  }

  AppException _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return const AppException.network('Connection timeout');
      case DioExceptionType.connectionError:
        return const AppException.network('No internet connection');
      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode ?? 0;
        final message = error.response?.data['message'] ?? 'Server error';

        switch (statusCode) {
          case 400:
            return AppException.badRequest(message);
          case 401:
            return AppException.unauthorized(message);
          case 403:
            return AppException.forbidden(message);
          case 404:
            return AppException.notFound(message);
          case 422:
            return AppException.validation(message);
          case 500:
            return AppException.server(message);
          default:
            return AppException.server(message);
        }

      case DioExceptionType.cancel:
        return const AppException.unknown('Request cancelled');

      case DioExceptionType.unknown:
      default:
        return const AppException.network('No internet connection');
    }
  }
}