import 'package:dartz/dartz.dart';

import '../../exceptions/http_exception.dart';

abstract class NetworkService {
  String get baseUrl;
  Map<String, String> get headers;

  Future<Either<AppException, dynamic>> get(
      String endpoint, {
        Map<String, dynamic>? queryParameters,
      });

  Future<Either<AppException, dynamic>> post(
      String endpoint, {
        Map<String, dynamic>? data,
      });

  Future<Either<AppException, dynamic>> put(
      String endpoint, {
        Map<String, dynamic>? data,
      });

  Future<Either<AppException, dynamic>> delete(String endpoint);
}