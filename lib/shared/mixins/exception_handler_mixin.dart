import 'package:dartz/dartz.dart';
import '../exceptions/http_exception.dart';

mixin ExceptionHandlerMixin {
  Future<Either<AppException, T>> handleException<T>(
      Future<T> Function() operation,
      ) async {
    try {
      final result = await operation();
      return Right(result);
    } on AppException catch (e) {
      return Left(e);
    } catch (e) {
      return Left(AppException.unknown(e.toString()));
    }
  }

  Either<AppException, T> handleSyncException<T>(
      T Function() operation,
      ) {
    try {
      final result = operation();
      return Right(result);
    } on AppException catch (e) {
      return Left(e);
    } catch (e) {
      return Left(AppException.unknown(e.toString()));
    }
  }
}