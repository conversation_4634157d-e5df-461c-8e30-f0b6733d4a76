import 'package:freezed_annotation/freezed_annotation.dart';

part 'http_exception.freezed.dart';

@freezed
class AppException with _$AppException implements Exception {
  const factory AppException.network(String message) = NetworkException;
  const factory AppException.badRequest(String message) = BadRequestException;
  const factory AppException.unauthorized(String message) = UnauthorizedException;
  const factory AppException.forbidden(String message) = ForbiddenException;
  const factory AppException.notFound(String message) = NotFoundException;
  const factory AppException.validation(String message) = ValidationException;
  const factory AppException.server(String message) = ServerException;
  const factory AppException.unknown(String message) = UnknownException;
}

extension AppExceptionX on AppException {
  String get message => when(
    network: (message) => message,
    badRequest: (message) => message,
    unauthorized: (message) => message,
    forbidden: (message) => message,
    notFound: (message) => message,
    validation: (message) => message,
    server: (message) => message,
    unknown: (message) => message,
  );
}
