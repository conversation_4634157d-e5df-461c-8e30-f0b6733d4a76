// Update your responsive_utils.dart file
// Rename the data classes to avoid conflicts with widgets

import 'package:flutter/material.dart';

/// Responsive utility class for consistent sizing across the app
class ResponsiveUtils {
  static ResponsiveUtils? _instance;
  static ResponsiveUtils get instance => _instance ??= ResponsiveUtils._();
  ResponsiveUtils._();

  /// Screen size breakpoints
  static const double mobileSmall = 360;
  static const double mobileMedium = 375;
  static const double mobileLarge = 414;
  static const double tablet = 768;
  static const double desktop = 1024;

  /// Height breakpoints
  static const double heightSmall = 500;
  static const double heightMedium = 600;
  static const double heightLarge = 800;

  /// Get responsive dimensions based on screen size
  static ResponsiveDimensions getDimensions(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final padding = MediaQuery.of(context).viewInsets;

    return ResponsiveDimensions(
      screenSize: size,
      padding: padding,
      isSmallScreen: size.height < heightMedium,
      isVerySmallScreen: size.height < heightSmall,
      isLargeScreen: size.height > heightLarge,
      isLandscape: size.width > size.height,
      isTablet: size.shortestSide >= tablet,
      isMobile: size.shortestSide < tablet,
      deviceType: _getDeviceType(size),
    );
  }

  static DeviceType _getDeviceType(Size size) {
    if (size.shortestSide >= desktop) return DeviceType.desktop;
    if (size.shortestSide >= tablet) return DeviceType.tablet;
    if (size.width >= mobileLarge) return DeviceType.mobileLarge;
    if (size.width >= mobileMedium) return DeviceType.mobileMedium;
    return DeviceType.mobileSmall;
  }

  /// Get responsive spacing values
  static ResponsiveSpacingData getSpacing(BuildContext context) {  // Renamed
    final dimensions = getDimensions(context);

    return ResponsiveSpacingData(  // Renamed
      dimensions: dimensions,
      xs: dimensions.isVerySmallScreen ? 4.0 : 8.0,
      sm: dimensions.isVerySmallScreen ? 8.0 : dimensions.isSmallScreen ? 12.0 : 16.0,
      md: dimensions.isVerySmallScreen ? 12.0 : dimensions.isSmallScreen ? 16.0 : 24.0,
      lg: dimensions.isVerySmallScreen ? 16.0 : dimensions.isSmallScreen ? 24.0 : 32.0,
      xl: dimensions.isVerySmallScreen ? 24.0 : dimensions.isSmallScreen ? 32.0 : 48.0,
    );
  }

  /// Get responsive typography
  static ResponsiveTypographyData getTypography(BuildContext context) {  // Renamed
    final dimensions = getDimensions(context);

    return ResponsiveTypographyData(  // Renamed
      dimensions: dimensions,
      h1: dimensions.isVerySmallScreen ? 24.0 : dimensions.isSmallScreen ? 28.0 : 32.0,
      h2: dimensions.isVerySmallScreen ? 20.0 : dimensions.isSmallScreen ? 24.0 : 28.0,
      h3: dimensions.isVerySmallScreen ? 18.0 : dimensions.isSmallScreen ? 20.0 : 24.0,
      h4: dimensions.isVerySmallScreen ? 16.0 : dimensions.isSmallScreen ? 18.0 : 20.0,
      bodyLarge: dimensions.isSmallScreen ? 16.0 : 18.0,
      bodyMedium: dimensions.isSmallScreen ? 14.0 : 16.0,
      bodySmall: dimensions.isSmallScreen ? 12.0 : 14.0,
      caption: dimensions.isSmallScreen ? 10.0 : 12.0,
    );
  }

  /// Get responsive button dimensions
  static ResponsiveButtonData getButton(BuildContext context) {  // Renamed
    final dimensions = getDimensions(context);

    return ResponsiveButtonData(  // Renamed
      dimensions: dimensions,
      heightLarge: dimensions.isSmallScreen ? 48.0 : 56.0,
      heightMedium: dimensions.isSmallScreen ? 40.0 : 48.0,
      heightSmall: dimensions.isSmallScreen ? 32.0 : 40.0,
      borderRadius: 12.0,
      fontSize: dimensions.isSmallScreen ? 14.0 : 16.0,
    );
  }

  /// Get responsive button dimensions (alternative method)
  static ResponsiveButtonDimensions getButtonDimensions(BuildContext context) {  // Renamed method
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 576) {
      return const ResponsiveButtonDimensions(
        heightSmall: 36,
        heightMedium: 44,
        heightLarge: 52,
        fontSize: 14,
        borderRadius: 8,
      );
    } else if (screenWidth < 768) {
      return const ResponsiveButtonDimensions(
        heightSmall: 40,
        heightMedium: 48,
        heightLarge: 56,
        fontSize: 15,
        borderRadius: 10,
      );
    } else {
      return const ResponsiveButtonDimensions(
        heightSmall: 44,
        heightMedium: 52,
        heightLarge: 60,
        fontSize: 16,
        borderRadius: 12,
      );
    }
  }

  /// Get responsive image dimensions
  static ResponsiveImageData getImage(BuildContext context) {  // Renamed
    final dimensions = getDimensions(context);

    return ResponsiveImageData(  // Renamed
      dimensions: dimensions,
      large: dimensions.isLandscape
          ? dimensions.screenSize.height * 0.4
          : dimensions.isVerySmallScreen
          ? 180.0
          : dimensions.isSmallScreen
          ? 220.0
          : 280.0,
      medium: dimensions.isVerySmallScreen ? 120.0 : dimensions.isSmallScreen ? 150.0 : 200.0,
      small: dimensions.isVerySmallScreen ? 80.0 : dimensions.isSmallScreen ? 100.0 : 120.0,
    );
  }
}

/// Responsive dimensions data class
class ResponsiveDimensions {
  final Size screenSize;
  final EdgeInsets padding;
  final bool isSmallScreen;
  final bool isVerySmallScreen;
  final bool isLargeScreen;
  final bool isLandscape;
  final bool isTablet;
  final bool isMobile;
  final DeviceType deviceType;

  const ResponsiveDimensions({
    required this.screenSize,
    required this.padding,
    required this.isSmallScreen,
    required this.isVerySmallScreen,
    required this.isLargeScreen,
    required this.isLandscape,
    required this.isTablet,
    required this.isMobile,
    required this.deviceType,
  });

  double get width => screenSize.width;
  double get height => screenSize.height;
  double get aspectRatio => width / height;

  /// Get responsive padding
  double get horizontalPadding => width < ResponsiveUtils.mobileSmall ? 16.0 : 24.0;
  double get verticalPadding => isVerySmallScreen ? 8.0 : isSmallScreen ? 12.0 : 16.0;
}

/// Responsive spacing data class - RENAMED
class ResponsiveSpacingData {
  final ResponsiveDimensions dimensions;
  final double xs;
  final double sm;
  final double md;
  final double lg;
  final double xl;

  const ResponsiveSpacingData({
    required this.dimensions,
    required this.xs,
    required this.sm,
    required this.md,
    required this.lg,
    required this.xl,
  });
}

/// Responsive typography data class - RENAMED
class ResponsiveTypographyData {
  final ResponsiveDimensions dimensions;
  final double h1;
  final double h2;
  final double h3;
  final double h4;
  final double bodyLarge;
  final double bodyMedium;
  final double bodySmall;
  final double caption;

  const ResponsiveTypographyData({
    required this.dimensions,
    required this.h1,
    required this.h2,
    required this.h3,
    required this.h4,
    required this.bodyLarge,
    required this.bodyMedium,
    required this.bodySmall,
    required this.caption,
  });
}

/// Responsive button data class - RENAMED
class ResponsiveButtonData {
  final ResponsiveDimensions dimensions;
  final double heightLarge;
  final double heightMedium;
  final double heightSmall;
  final double borderRadius;
  final double fontSize;

  const ResponsiveButtonData({
    required this.dimensions,
    required this.heightLarge,
    required this.heightMedium,
    required this.heightSmall,
    required this.borderRadius,
    required this.fontSize,
  });
}

/// Keep this class as is for the button widget
class ResponsiveButtonDimensions {
  final double heightSmall;
  final double heightMedium;
  final double heightLarge;
  final double fontSize;
  final double borderRadius;

  const ResponsiveButtonDimensions({
    required this.heightSmall,
    required this.heightMedium,
    required this.heightLarge,
    required this.fontSize,
    required this.borderRadius,
  });
}

/// Responsive image data class - RENAMED
class ResponsiveImageData {
  final ResponsiveDimensions dimensions;
  final double large;
  final double medium;
  final double small;

  const ResponsiveImageData({
    required this.dimensions,
    required this.large,
    required this.medium,
    required this.small,
  });
}

/// Device type enumeration
enum DeviceType {
  mobileSmall,
  mobileMedium,
  mobileLarge,
  tablet,
  desktop,
}

/// Extension methods for easier usage - UPDATED
extension ResponsiveContext on BuildContext {
  ResponsiveDimensions get responsive => ResponsiveUtils.getDimensions(this);
  ResponsiveSpacingData get spacing => ResponsiveUtils.getSpacing(this);
  ResponsiveTypographyData get typography => ResponsiveUtils.getTypography(this);
  ResponsiveButtonData get button => ResponsiveUtils.getButton(this);
  ResponsiveImageData get image => ResponsiveUtils.getImage(this);
}

/// Responsive widget builder
class ResponsiveBuilder extends StatelessWidget {
  final Widget Function(BuildContext context, ResponsiveDimensions dimensions) builder;

  const ResponsiveBuilder({
  super.key,
  required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    return builder(context, context.responsive);
  }
}

/// Responsive layout widget for different screen sizes
class ResponsiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;

  const ResponsiveLayout({
  super.key,
  required this.mobile,
  this.tablet,
  this.desktop,
  });

  @override
  Widget build(BuildContext context) {
    final dimensions = context.responsive;

    if (dimensions.deviceType == DeviceType.desktop && desktop != null) {
      return desktop!;
    }

    if (dimensions.isTablet && tablet != null) {
      return tablet!;
    }

    return mobile;
  }
}