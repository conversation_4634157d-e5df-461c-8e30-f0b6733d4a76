import '../database/app_database.dart';
import '../network/network_info.dart';

abstract class SyncManager {
  Future<void> queueForSync(String operation, Map<String, dynamic> data);
  Future<void> syncPendingOperations();
  Future<int> getPendingOperationsCount();
  Future<void> removeSyncOperation(String operation, String id);
}

class Sync<PERSON>anagerImpl implements SyncManager {
  final AppDatabase _database;
  final NetworkInfo _networkInfo;

  SyncManagerImpl(this._database, this._networkInfo);

  @override
  Future<void> queueForSync(String operation, Map<String, dynamic> data) async {
    final syncItem = {
      'id': DateTime.now().millisecondsSinceEpoch.toString(),
      'operation': operation,
      'data': data,
      'timestamp': DateTime.now().toIso8601String(),
      'retryCount': 0,
    };

    final currentQueue = AppDatabase.syncBox.get('queue', defaultValue: <Map<String, dynamic>>[]);
    currentQueue.add(syncItem);
    await AppDatabase.syncBox.put('queue', currentQueue);
  }

  @override
  Future<void> syncPendingOperations() async {
    if (!await _networkInfo.isConnected) return;

    final queue = AppDatabase.syncBox.get('queue', defaultValue: <Map<String, dynamic>>[]);
    final successfulSyncs = <Map<String, dynamic>>[];

    for (final item in queue) {
      try {
        await _executeSyncOperation(item);
        successfulSyncs.add(item);
      } catch (e) {
        // Increment retry count
        item['retryCount'] = (item['retryCount'] ?? 0) + 1;

        // Remove if max retries exceeded
        if (item['retryCount'] >= 3) {
          successfulSyncs.add(item);
        }
      }
    }

    // Remove successful/failed syncs from queue
    final remainingQueue = queue.where((item) => !successfulSyncs.contains(item)).toList();
    await AppDatabase.syncBox.put('queue', remainingQueue);
  }

  Future<void> _executeSyncOperation(Map<String, dynamic> item) async {
    final operation = item['operation'] as String;
    final data = item['data'] as Map<String, dynamic>;

    switch (operation) {
      case 'reorder_favorites':
      // Call remote API to sync favorite order
        break;
      case 'update_balance_visibility':
      // Sync balance visibility preference
        break;
    // Add other sync operations
    }
  }

  @override
  Future<int> getPendingOperationsCount() async {
    final queue = AppDatabase.syncBox.get('queue', defaultValue: <Map<String, dynamic>>[]);
    return queue.length;
  }

  @override
  Future<void> removeSyncOperation(String operation, String id) async {
    final queue = AppDatabase.syncBox.get('queue', defaultValue: <Map<String, dynamic>>[]);
    queue.removeWhere((item) =>
    item['operation'] == operation &&
        (item['data']['id'] == id || item['id'] == id)
    );
    await AppDatabase.syncBox.put('queue', queue);
  }
}