// core/database/app_database.dart
import 'package:hive_flutter/hive_flutter.dart';

class AppDatabase {
  static late Box _userBox;
  static late Box _homeBox;
  static late Box _favoritesBox;
  static late Box _bannersBox;
  static late Box _syncBox;

  static Future<void> initialize() async {
    await Hive.initFlutter();

    _userBox = await Hive.openBox('user_data');
    _homeBox = await Hive.openBox('home_data');
    _favoritesBox = await Hive.openBox('favorites');
    _bannersBox = await Hive.openBox('banners');
    _syncBox = await Hive.openBox('sync_queue');
  }

  static Box get userBox => _userBox;
  static Box get homeBox => _homeBox;
  static Box get favoritesBox => _favoritesBox;
  static Box get bannersBox => _bannersBox;
  static Box get syncBox => _syncBox;
}